# 后端请求地址
VITE_API_BASE=https://copilot.sino-bridge.com/api
# 服务前缀
VITE_API_BASE_PUB=/langwell-pub-server
VITE_API_BASE_SYS=/langwell-sys-server
VITE_API_BASE_NOTE=/langwell-notes-server
VITE_API_BASE_AI=/langwell-ai-server
VITE_API_BASE_INS=/langwell-ins-server
VITE_API_BASE_DOC=/langwell-doc-server

# API凭据key
VITE_API_HEADER_KEY=OVERTOKEN
# 授权方式
VITE_AUTHORIZE_MODE=account
# AI后端请求地址
VITE_AI_API_BASE=https://copilot.sino-bridge.com:90/v1
VITE_AI_CHAT_SECRET=app-lurFZzGKnEfUytwW5zV64ySC
VITE_AI_CHAT_AGENT_ID=0
# VITE_AI_WRITER_SECRET=app-ja6HUI7Qfa4rEvCPTITTpyeG
VITE_AI_WRITER_SECRET=99991
# VITE_AI_REPLY_SECRET=app-7bfg5jwzV8EUu0MxqO6Zv4FO
VITE_AI_REPLY_SECRET=99992

# 租户用户信息
VITE_USERINFO_BASS=https://copilot.sino-bridge.com:84/api
# 官网页面地址
VITE_OFFICIAL_URL=https://copilot.sino-bridge.com:85

# 授权相关配置
VITE_CORP_ID=wwd7e14e07bf973c80
VITE_AGENT_ID=1000085
VITE_AUTHORIZE_DOMAIN=https://scrm.sino-bridge.com:8098

# 文件前缀
VITE_FILE_PREFIX=https://copilot.sino-bridge.com:90

# 是否开启实验功能
VITE_ENABLE_LAB_FEATURES=false

# 工具地址
VITE_TOOLBOX_URL=https://copilot.sino-bridge.com/toolbox

# 新场景地址
VITE_COPILOT_URL=https://copilot.sino-bridge.com:85/copilot

# mqtt配置
VITE_MQTT_PROTOCOL=wss
VITE_MQTT_HOST=copilot.sino-bridge.com
VITE_MQTT_PORT=85
VITE_MQTT_USERNAME=your-mqtt-username
VITE_MQTT_PASSWORD=your-mqtt-password
VITE_MQTT_PATH=/mqttSocket/mqtt

# 网页端地址
VITE_WEB_URL=https://copilot.sino-bridge.com:85/copilot-web-app-lnjk/login

# 数字人地址
VITE_NUMBER_PERSON_PATH=https://ff8418a.r21.vip.cpolar.cn/