# 后端请求地址
VITE_API_BASE=http://*************/langwell-api

# 服务前缀
VITE_API_BASE_PUB=/langwell-pub-server
VITE_API_BASE_SYS=/langwell-sys-server
VITE_API_BASE_NOTE=/langwell-notes-server
VITE_API_BASE_AI=/langwell-ai-server
VITE_API_BASE_INS=/langwell-ins-server
VITE_API_BASE_DOC=/langwell-doc-server

# API凭据key
VITE_API_HEADER_KEY=OVERTOKEN
# 授权方式
VITE_AUTHORIZE_MODE=account
# AI后端请求地址
VITE_AI_API_BASE=http://*************:88/v1
VITE_AI_CHAT_SECRET=app-auOHiigIlp5NPY6LqCMoV0II
VITE_AI_CHAT_AGENT_ID=673cc468-80ad-4904-9005-59714d6cf6b5
VITE_AI_WRITER_SECRET=fde9cea7-2151-48a8-ace0-7706244b9a59 
VITE_AI_REPLY_SECRET=2114515d-fdb9-410e-af3a-d1e118b71d39

# 租户用户信息
VITE_USERINFO_BASS=http:*************/lamp-api
# 官网页面地址
VITE_OFFICIAL_URL=http://*************

# 文件前缀
VITE_FILE_PREFIX=http://*************:88

# 是否开启实验功能
VITE_ENABLE_LAB_FEATURES=false

# 工具地址
VITE_TOOLBOX_URL=http://*************/toolbox

# mqtt配置
VITE_MQTT_PROTOCOL=ws
VITE_MQTT_HOST=*************
VITE_MQTT_PORT=80
VITE_MQTT_USERNAME=your-mqtt-username
VITE_MQTT_PASSWORD=your-mqtt-password
VITE_MQTT_PATH=/mqttSocket/mqtt
