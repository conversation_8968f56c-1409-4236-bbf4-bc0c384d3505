import React, { useEffect } from "react";
import setModifyItem, { NOTE_MODIFY_STORAGE_KEY } from "@/utils/browserStorageCurrentPage";
import { message } from "antd";
import { SendByPrompt } from "@/types/chat";

const CreateNote: React.FC = () => {
  const fetchRequest = useFetchRequest();
  const addNoteRequest = (noteElement, target, info, agent) => {
    console.log("🔍 addNoteRequest called with:", { noteElement, target, info, agent });
    noteElement.color = "#FEFFE6"; // 默认颜色
    fetchRequest({
      api: "addNote",
      params: noteElement,
      callback: async (res) => {
        console.log("🔍 addNote API response:", res);
        if (res.code === 200) {
          let note = {
            ...res.data,
            displayFlag: 1,
          };
          if (agent && agent.id) {
            note.promptId = agent.id;
            note.promptTitle = agent.title;
            note.promptContent = agent.content;
            await sendByPrompt({
              noteId: res.data.id,
              promptId: agent.id,
              promptTitle: agent.title,
              promptContent: agent.content,
            });
          }

          setModifyItem(NOTE_MODIFY_STORAGE_KEY, { ...res.data, key: new Date().getTime(), updateType: "add" });
          console.log("🔍 sending setNoteNotice with:", { note, target, info });
          window.postMessage(
            {
              type: "setNoteNotice",
              note,
              target,
              info,
            },
            "*",
          );
        } else {
          // 创建报错了给出提示，便签不渲染
          message.open({
            type: "error",
            content: res.msg,
          });
        }
      },
    });
  };

  const sendByPrompt = (prompt: SendByPrompt) => {
    fetchRequest({
      api: "addNotePromptRela",
      params: prompt,
      callback: (res) => {
        if (res.code !== 200) {
          message.open({
            type: "error",
            content: res.msg,
          });
        }
      },
    });
  };

  useEffect(() => {
    const receiveMessage = (event) => {
      if (event.data.type === "addNoteNotice") {
        console.log("🔍 CreateNote received addNoteNotice:", event.data);
        // 在这里处理接收到的消息
        addNoteRequest(event.data.note, event.data.target, event.data.info, event.data.agent);
      }
    };
    window.addEventListener("message", receiveMessage);
    return () => {
      window.removeEventListener("message", receiveMessage);
    };
  }, []);

  return <></>;
};
export default CreateNote;
