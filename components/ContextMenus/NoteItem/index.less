@import "@/assets/styles/variables";

.resizer {
  width: 16px;
  height: 16px;
  position: absolute;
  right: 0px;
  bottom: 0px;
  cursor: se-resize;

  &::after {
    content: "";
    position: absolute;
    bottom: 0;
    right: 0;
    transform: rotate(45deg);
    transform-origin: center;
    width: 0;
    height: 0;
    border: 6px solid transparent;
    border-left-color: #e1e1e1;
  }
}

.sino-note-info {
  position: absolute;
  top: 14px;
  left: 16px;
  z-index: 12;
}
.note-ts{
  width: 100%;
  height:16px;
  position:absolute;
  left:0px;
  top:0px;
  opacity: 0;
  margin: 0px;
}
.opactive{
  width: 100%;
  height:16px;
  position:absolute;
  left:0px;
  top:0px;
  opacity: 0;
}

.sino-config-panel {
  position: absolute;
}

.ant-popconfirm {
  z-index: 10600 !important;
}
