import { TinyEmitter } from "tiny-emitter";
import { ListenerCollection } from "../shared/listener-collection";
import { PortFinder, PortRPC } from "../shared/messaging";
import type {
  AnnotationData,
  Annotator,
  Anchor,
  Destroyable,
  DocumentInfo,
  Integration,
  SidebarLayout,
} from "../types/annotator";
import type { Target } from "../types/api";
import type {
  HostToGuestEvent,
  GuestToHostEvent,
  GuestToSidebarEvent,
  SidebarToGuestEvent,
} from "../types/port-rpc-events";
import { Adder } from "./adder";
import { TextRange } from "./anchoring/text-range";
import { BucketBarClient } from "./bucket-bar-client";
import {
  getHighlightsContainingNode,
  highlightRange,
  removeAllHighlights,
  removeHighlights,
  setHighlightsFocused,
  wholeTextNodesInRange,
} from "./highlighter";
import { HTMLIntegration } from "./integrations";
import { itemsForRange, isSelectionBackwards, selectionFocusRect, selectedRange } from "./range-util";
import { SelectionObserver } from "./selection-observer";
import { normalizeURI } from "./util/url";
import { getUrlNoQuery, NOTE_THEME, NOTE_MIN_WIDTH, NOTE_MIN_HEIGHT } from "@/utils/notes";
import type { Note } from "@/types/note";
import { AgentInfo } from "@/types/chat";

/** 高亮器创建的 HTML 元素，带有相关的注释。 */
type AnnotationHighlight = HTMLElement & { _annotation?: AnnotationData };

/** 返回与选定文本相关的所有注释标签。 */
function annotationsForSelection(): string[] {
  const tags = itemsForRange(selectedRange() ?? new Range(), (node) => (node as AnnotationHighlight)._annotation?.$tag);
  return tags;
}

/**
 * 返回包含给定 DOM 节点的任何高亮的注释标签。
 */
function annotationsAt(node: Node): string[] {
  const items = getHighlightsContainingNode(node)
    .map((h) => (h as AnnotationHighlight)._annotation)
    .filter((ann) => ann !== undefined)
    .map((ann) => ann?.$tag);
  return items as string[];
}

/**
 * 将锚点的相关文档区域解析为具体的 `Range`。
 *
 * 如果锚点创建后文档发生了变更，导致锚点无效，则可能会失败。
 */
function resolveAnchor(anchor: Anchor): Range | null {
  if (!anchor.range) {
    return null;
  }
  try {
    return anchor.range.toRange();
  } catch {
    return null;
  }
}

function removeTextSelection() {
  document.getSelection()?.removeAllRanges();
}

/**
 * Hypothesis 客户端配置的子集，用于 {@link Guest}。
 */
export type GuestConfig = {
  /**
   * 在尝试锚定注释之前，Guest 应等待的 Promise。
   */
  contentReady?: Promise<void>;
};

/**
 * 客户端在即将滚动高亮到视图中时分派的事件。
 *
 * 主页面可以监听此事件以显示内容（如果尚未可见）。如果内容将异步显示，可以使用 {@link waitUntil} 通知客户端何时准备就绪。
 *
 * 为了更灵活，主页面可以通过在事件上调用 {@link Event.preventDefault} 完全接管滚动到范围的操作。
 */
export class ScrollToRangeEvent extends CustomEvent<Range> {
  private _ready: Promise<void> | null;

  /**
   * @param range - Hypothesis 将滚动到视图中的 DOM 范围。
   */
  constructor(range: Range) {
    super("scrolltorange", {
      bubbles: true,
      cancelable: true,
      detail: range,
    });

    this._ready = null;
  }

  /**
   * 如果使用 {@link waitUntil} 延迟了滚动，则返回在高亮滚动到视图之前必须解析的 Promise。
   */
  get ready(): Promise<void> | null {
    return this._ready;
  }

  /**
   * 提供一个 Promise，当与事件范围相关的内容准备好滚动到视图时解析。
   */
  waitUntil(ready: Promise<void>) {
    this._ready = ready;
  }
}

/**
 * `Guest` 是注释器的核心类，负责在从侧边栏获取注释时在文档中定位注释、为其渲染高亮并处理与高亮的后续交互。
 *
 * 它还负责监听当前选择的更改并触发显示创建新注释的控件。当点击这些控件之一时，它会创建新注释并将其发送到侧边栏。
 *
 * 在浏览器标签页中，通常每个加载 Hypothesis 的框架有一个 `Guest` 实例（并非所有框架都启用注释）。在一个框架中，通常是顶级框架，还会有一个显示侧边栏应用和周围 UI 的 `Sidebar` 类实例。每个框架中的 `Guest` 实例在初始化时连接到侧边栏和主框架。
 */
export class Guest extends TinyEmitter implements Annotator, Destroyable {
  public element: HTMLElement;

  /** 当前文本选择的范围。 */
  public selectedRanges: Range[];

  /**
   * 通过将注释选择器解析为文档中的位置生成的锚点。这些由 `anchor` 添加，由 `detach` 移除。
   *
   * 每个注释 `Target` 通常有一个锚点，这通常意味着每个注释有一个锚点。
   */
  public anchors: Anchor[];

  /**
   * 在尝试锚定注释之前，Guest 将等待的 Promise。
   */
  private _contentReady?: Promise<void>;

  private _adder: Adder;
  private _hostFrame: Window;
  private _isAdderVisible: boolean;
  private _range: Range;
  private _selectedText: string;
  private _informHostOnNextSelectionClear: boolean;
  private _selectionObserver: SelectionObserver;

  /**
   * 当前在 Guest 中锚定或正在锚定的注释标签。
   */
  private _annotations: Set<string>;
  private _frameIdentifier: string | null;
  private _portFinder: PortFinder;

  /**
   * 处理 Guest 中文档类型特定功能的集成。
   */
  private _integration: Integration;

  /** 主客通信通道。 */
  private _hostRPC: PortRPC<HostToGuestEvent, GuestToHostEvent>;

  /** 客侧边栏通信通道。 */
  private _sidebarRPC: PortRPC<SidebarToGuestEvent, GuestToSidebarEvent>;

  /**
   * 从主框架接收到的最新侧边栏布局信息。
   */
  private _sidebarLayout: SidebarLayout | null;

  private _bucketBarClient: BucketBarClient;

  private _listeners: ListenerCollection;

  /**
   * 当前悬停注释的标签。这用于在关联的注释在侧边栏中已经悬停时正确设置新高亮的悬停状态。
   */
  private _hoveredAnnotations: Set<string>;
  private _createAnnotationBase: {
    type: string;
    agent: null | AgentInfo;
    note: null | Note;
    target: null | Target[];
    info: null | DocumentInfo;
  };

  /**
   * @param element -
   *   `Guest` 实例应能够锚定或创建注释的根元素。在普通网页中，这通常是 `document.body`。
   * @param [config]
   * @param [hostFrame] -
   *   与此 Guest 关联的主框架。预计这是 Guest 框架的祖先。它可以是同源或跨源的。
   */
  constructor(element: HTMLElement, config: GuestConfig = {}, hostFrame: Window = window) {
    super();
    this.element = element;
    this._contentReady = config.contentReady;
    this._hostFrame = hostFrame;
    this._isAdderVisible = false;
    this._informHostOnNextSelectionClear = true;
    this.selectedRanges = [];
    this._createAnnotationBase = {
      type: "",
      agent: null,
      note: null,
      target: null,
      info: null,
    };
    this._adder = new Adder(this.element, {
      onAnnotate: (type, obj?: AgentInfo) => this.createAnnotation(type, obj, false),
      onHighlight: (type) => this.createAnnotation(type, null, false),

      // 当触发“显示”按钮时，打开侧边栏并选择注释。还将键盘焦点转移到第一个选定的注释。这对于屏幕阅读器用户来说是一个重要的便利，因为它为他们提供了一种从文档中的高亮快速导航到侧边栏中相应评论的高效方式。
      onShowAnnotations: (tags) => this.selectAnnotations(tags),
    });

    this._selectionObserver = new SelectionObserver((range) => {
      console.log("🔍 SelectionObserver callback triggered with range:", range);
      if (range) {
        this._onSelection(range);
      } else {
        this._onClearSelection();
      }
    });

    this.anchors = [];
    this._annotations = new Set();

    // 如果可用，设置框架标识符。
    // “顶级” Guest 实例将其设置为 null，因为它在顶级框架中，而不是子框架中
    this._frameIdentifier = null;

    this._portFinder = new PortFinder({
      hostFrame: this._hostFrame,
      source: "guest",
      sourceId: this._frameIdentifier ?? undefined,
    });
    this._integration = new HTMLIntegration({});
    this._integration.on("uriChanged", () => this._sendDocumentInfo());

    this._hostRPC = new PortRPC();
    this._connectHost(hostFrame);

    this._sidebarRPC = new PortRPC();
    this._sidebarLayout = null;
    this._connectSidebar();

    this._bucketBarClient = new BucketBarClient({
      contentContainer: this._integration.contentContainer(),
      hostRPC: this._hostRPC,
    });

    // 在根元素上设置事件处理程序
    this._listeners = new ListenerCollection();
    this._setupElementEvents();

    this._hoveredAnnotations = new Set();
  }

  // 为文档和高亮上的点击、点击等添加 DOM 事件监听器。
  _setupElementEvents() {
    this._listeners.add(window, "resize", () => this._repositionAdder());
  }

  /**
   * 检索当前文档的元数据。
   */
  async getDocumentInfo(): Promise<DocumentInfo> {
    const [uri, metadata, segmentInfo] = await Promise.all([
      this._integration.uri(),
      this._integration.getMetadata(),
      this._integration.segmentInfo?.(),
    ]);

    return {
      uri: normalizeURI(uri),
      metadata,
      segmentInfo,
      persistent: this._integration.persistFrame?.() ?? false,
    };
  }

  /** 将当前文档 URI 和元数据发送到侧边栏。 */
  async _sendDocumentInfo() {
    const metadata = await this.getDocumentInfo();
    this._sidebarRPC.call("documentInfoChanged", metadata);
  }

  /**
   * 在窗口“resize”事件时调整添加器的位置
   */
  _repositionAdder() {
    if (!this._isAdderVisible) {
      return;
    }
    const range = selectedRange();
    if (range) {
      this._onSelection(range);
    }
  }

  async _connectHost(hostFrame: Window) {
    this._hostRPC.on("clearSelection", () => {
      if (selectedRange()) {
        this._informHostOnNextSelectionClear = false;
        removeTextSelection();
      }
    });

    this._hostRPC.on("setNoteNotice", async (note: Note, target, info) => {
      console.log("🔍 setNoteNotice received:", { note, target, info });
      const annotation: AnnotationData = {
        uri: note.url,
        document: info?.metadata || "",
        target,
        $highlight: false,
        $cluster: "user-annotations",
        $tag: note.id,
        $id: note.id,
        item: note,
      };
      console.log("🔍 annotation created:", annotation);
      await this.anchor(annotation);
      this._adder.setNotelist(note);
      // 删除文本选择会触发 `SelectionObserver` 回调，这会导致添加器在一段时间后被移除。
      removeTextSelection();
    });

    this._hostRPC.on("delNoteNotice", async (note: Note) => {
      this.detach(note.id);
      this._adder.delNotelistByTag(note.id);
    });

    this._hostRPC.on("hoverAnnotations", (tags: string[]) => this._hoverAnnotations(tags));

    this._hostRPC.on("scrollToAnnotation", (tag: string) => {
      this._scrollToAnnotation(tag);
    });

    this._hostRPC.on("selectAnnotations", (tags: string[]) => this.selectAnnotations(tags));

    this._hostRPC.on("close", () => this.emit("hostDisconnected"));

    // 发现并连接到主框架。所有 RPC 事件必须在创建通道之前注册。
    const hostPort = await this._portFinder.discover("host");
    this._hostRPC.connect(hostPort);
  }

  /**
   * 滚动锚点到视图并通知主页面。
   *
   * 返回一个 Promise，当滚动完成时解析。参见 {@link Integration.scrollToAnchor}。
   */
  private async _scrollToAnchor(anchor: Anchor) {
    const range = resolveAnchor(anchor);
    if (!range) {
      return;
    }

    // 触发主页面可以响应的自定义事件。如果内容在页面的隐藏部分，需要在滚动到视图之前显示出来，这很有用。
    const event = new ScrollToRangeEvent(range);

    const defaultNotPrevented = this.element.dispatchEvent(event);

    if (defaultNotPrevented) {
      await event.ready;
      await this._integration.scrollToAnchor(anchor);
    }
  }

  private async _scrollToAnnotation(tag: string) {
    const anchor = this.anchors.find((a) => a.annotation.$tag === tag);
    if (!anchor?.highlights) {
      return;
    }
    await this._scrollToAnchor(anchor);
  }

  async _connectSidebar() {
    // 处理用户在侧边栏中悬停或点击注释卡片时发送的事件。
    this._sidebarRPC.on("hoverAnnotations", (tags: string[]) => this._hoverAnnotations(tags));

    this._sidebarRPC.on("scrollToAnnotation", (tag: string) => {
      this._scrollToAnnotation(tag);
    });

    this._sidebarRPC.on("deleteAnnotation", (tag: string) => this.detach(tag));

    this._sidebarRPC.on("loadAnnotations", async (annotations: AnnotationData[]) => {
      try {
        await Promise.all(annotations.map((ann) => this.anchor(ann)));
      } catch (e) {
        /* istanbul ignore next */
        console.warn("Failed to anchor annotations:", e);
      }
    });

    // 连接到侧边栏并将文档信息/URI 发送到侧边栏。
    //
    // RPC 调用在建立连接之前是延迟的，因此这些步骤可以按任意顺序完成。
    this._portFinder.discover("sidebar").then((port) => {
      this._sidebarRPC.connect(port);
    });

    this._sendDocumentInfo();
  }

  destroy() {
    this._portFinder.destroy();
    this._hostRPC.destroy();
    this._sidebarRPC.destroy();

    this._listeners.removeAll();

    this._selectionObserver.disconnect();
    this._adder.destroy();
    this._bucketBarClient.destroy();

    removeAllHighlights(this.element);

    this._integration.destroy();
  }
  /**
   * 锚定文档中的注释选择器。
   *
   * 锚定将一组选择器解析为文档中的具体区域，然后对其进行高亮显示。
   *
   * 在重新锚定注释之前，将删除与 `annotation` 关联的任何现有锚点。
   */
  async anchor(annotation: AnnotationData): Promise<Anchor[]> {
    if (this._contentReady) {
      await this._contentReady;
      this._contentReady = undefined;
    }

    /**
     * 将注释的选择器解析为具体的范围。
     */
    const locate = async (target: Target): Promise<Anchor> => {
      console.log("🔍 locate target:", target);
      // 目前只有带有相关引用的注释可以被锚定。
      // 这是因为引用用于验证其他选择器类型的锚定。
      if (!target.selector || !target.selector.some((s) => s.type === "TextQuoteSelector")) {
        console.log("🔍 target has no TextQuoteSelector, returning anchor without range");
        return { annotation, target };
      }

      let anchor: Anchor;
      try {
        const range = await this._integration.anchor(this.element, target.selector);
        // 将 `Range` 转换为 `TextRange`，可以稍后再转换回 `Range`。
        // `TextRange` 表示允许在锚定其他注释期间插入高亮而不会“破坏”此锚点。
        const textRange = TextRange.fromRange(range);
        anchor = { annotation, target, range: textRange };
        console.log("🔍 anchor created with range:", anchor);
      } catch (err) {
        console.log("🔍 error creating anchor range:", err);
        anchor = { annotation, target };
      }
      return anchor;
    };

    /**
     * 高亮 `anchor` 所指的文本范围。
     */
    const highlight = (anchor: Anchor) => {
      console.log("🔍 highlight called with anchor:", anchor);
      const range = resolveAnchor(anchor);
      console.log("🔍 resolveAnchor result:", range);
      if (!range) {
        console.log("🔍 highlight method exiting early - no range found");
        return;
      }

      const highlights = highlightRange(
        range,
        anchor.annotation?.$cluster /* cssClass */,
        anchor.annotation?.$id,
      ) as AnnotationHighlight[];
      highlights.forEach((h) => {
        h._annotation = anchor.annotation;
      });
      anchor.highlights = highlights;

      if (this._hoveredAnnotations.has(anchor.annotation.$tag)) {
        setHighlightsFocused(highlights, true);
      }
      console.log("🔍 highlight method completed successfully");
    };

    // 删除此注释的现有锚点。
    this.detach(annotation.$tag, false /* notify */);

    this._annotations.add(annotation.$tag);

    // 将选择器解析为范围并插入高亮。
    if (!annotation.target) {
      annotation.target = [];
    }
    const anchors = await Promise.all(annotation.target.map(locate));
    // 如果在锚定过程中删除了注释，则不要保存锚点。
    if (!this._annotations.has(annotation.$tag)) {
      return [];
    }

    for (const anchor of anchors) {
      highlight(anchor);
    }

    // 设置指示锚定是否成功的标志。对于每个目标，如果没有选择器（即这是一个页面注释）或我们成功地将选择器解析为范围，则锚定成功。
    annotation.$orphan = anchors.length > 0 && anchors.every((anchor) => anchor.target.selector && !anchor.range);
    this.anchors = this.anchors.concat(anchors);
    this._updateAnchors(this.anchors, true /* notify */);

    return anchors;
  }

  _initNotelist(noteList) {
    this._adder.initNotelist(noteList);
  }
  // 展示隐藏note
  _setNoteShowClose(tag: string, type: boolean) {
    if (["1", "2"].includes(tag)) {
      this._adder.setNoteShowClose(type); // 全部展开收起
    } else {
      this._adder.setNotelistByTag(tag, type); // 单个展开收起
    }
    this._hostRPC.call("setNoteShowClose", tag, type);
  }
  // 删除所有得note
  _delAllNoteNotice(tags: string[]) {
    tags.forEach((tag) => {
      this.detach(tag);
    });
    this._adder.delAllNotelist();
  }

  /**
   * 从文档中删除注释的锚点和相关的高亮。
   *
   * @param [notify] - 仅供内部使用。是否通知主框架锚点的移除。
   */
  detach(tag: string, notify = true) {
    this._annotations.delete(tag);

    const anchors = [] as Anchor[];
    for (const anchor of this.anchors) {
      if (anchor.annotation.$tag !== tag) {
        anchors.push(anchor);
      } else if (anchor.highlights) {
        removeHighlights(anchor.highlights);
      }
    }
    this.anchors = anchors;
    this._updateAnchors(anchors, notify);
  }

  _updateAnchors(anchors: Anchor[], notify: boolean) {
    const bouketAnchors = anchors.filter((x) => {
      if (!x.range) {
        return false;
      }
      const textNodes = wholeTextNodesInRange(x.range.toRange());
      const nodeValue = textNodes[0]?.nodeValue;
      return /^空白元素-\d{13}$/.test(nodeValue);
    });
    if (notify) {
      this._bucketBarClient.update(bouketAnchors);
    }
  }

  /**
   * 创建一个与当前文档选定区域相关的新注释。
   * 主要分为两种情况
   * 1. 创建普通便签，通过手动js创建空白dom,并使用js选中内容，触发浏览器选中内容变化的监听SelectionObserver，触发得到一个根标签target
   * 2. 划词主动触发文本选中触发监听SelectionObserver,展示划词悬浮窗（this._adder.show(focusRect, isBackwards);），点击按钮触发
   *    1）获取划词内容，通过手动js创建空白dom,并使用js选中内容，触发浏览器选中内容变化的监听,得到两个根标签,即target自己创建的依赖dom, relTarget用户选中的文本
   *
   * @param options
   *   @param [options.highlight] - 如果为 true，则新注释将设置 `$highlight` 标志，导致其立即保存而无需提示评论。
   *  @param addType 是否要触发addNoteNotice
   * @return 新注释
   */
  async createAnnotation(type = "PLAIN", agent: AgentInfo | null, addType: boolean, id?) {
    const ranges = this.selectedRanges;
    this.selectedRanges = [];
    const info = await this.getDocumentInfo();
    const root = this.element;

    // 检查是否是iframe划词
    const isIframeSelection = (this as any)._isIframeSelection;
    const iframeSelectedText = (this as any)._iframeSelectedText;
    const iframeRect: DOMRect | null = (this as any)._iframeGlobalRect;

    let baseId = id || new Date().getTime();
    function getGlobalRectFromIframe(iframe: HTMLIFrameElement | null, rect: DOMRect): DOMRect {
      let x = rect.left;
      let y = rect.top;
      let width = rect.width;
      let height = rect.height;

      let currentIframe = iframe;

      while (currentIframe) {
        const iframeRect = currentIframe.getBoundingClientRect();
        const win = currentIframe.ownerDocument.defaultView;

        x += iframeRect.left + (win?.scrollX || 0);
        y += iframeRect.top + (win?.scrollY || 0);

        // 如果父层还是 iframe，继续往上递归
        if (win?.frameElement instanceof HTMLIFrameElement) {
          currentIframe = win.frameElement;
        } else {
          currentIframe = null;
        }
      }

      return new DOMRect(x, y, width, height);
    }

    if (!this._createAnnotationBase.type) {
      let target, coordinates, quoteContent;

      if (isIframeSelection && iframeRect) {
        // iframe 划词：使用传递的文本和坐标（修正为全局坐标）
        const globalRect = getGlobalRectFromIframe(window.frameElement as HTMLIFrameElement, iframeRect);

        target = [
          {
            source: info.uri,
            selector: [], // iframe 划词不需要 selectors，因为不在当前页面
          },
        ];

        coordinates = {
          top: globalRect.top,
          left: globalRect.left,
          width: globalRect.width,
          height: globalRect.height,
        };
        quoteContent = iframeSelectedText;

        // 清除 iframe 划词标记
        (this as any)._isIframeSelection = false;
        (this as any)._iframeSelectedText = null;
        (this as any)._iframeGlobalRect = null;
      } else {
        // 页面划词：使用原有逻辑
        console.log("🔍 createAnnotation - ranges:", ranges);
        const rangeSelectors = await Promise.all(ranges.map((range) => this._integration.describe(root, range)));
        console.log("🔍 createAnnotation - rangeSelectors:", rangeSelectors);
        target = rangeSelectors.map((selectors) => ({
          source: info.uri,
          selector: selectors,
        }));
        console.log("🔍 createAnnotation - target created:", target);

        const rect = this._range.getBoundingClientRect();
        const globalRect = new DOMRect(rect.left, rect.top + window.scrollY, rect.width, rect.height);

        coordinates = {
          top: globalRect.top,
          left: globalRect.left,
          width: globalRect.width,
          height: globalRect.height,
        };
        quoteContent = this._selectedText.includes("空白元素") ? "" : this._selectedText;
        console.log("🔍 createAnnotation - quoteContent:", quoteContent);
      }

      let note: Note = {
        content: "",
        title: type === "QUOTE" ? quoteContent.substring(0, 15) : type === "AI" ? agent?.title : "",
        quoteContent,
        type,
        displayFlag: 1,
        delEnable: 1,
        noteStyle: {
          noteType: "absolute",
          noteTop: 0,
          noteLeft: 0,
          noteWidth: NOTE_MIN_WIDTH,
          noteHeight: NOTE_MIN_HEIGHT,
        },
        editable: true,
        url: getUrlNoQuery(),
        color: NOTE_THEME,
        tag: JSON.stringify({ target, coordinates, baseId }),
        belongUserId: "",
        readFlag: "",
        count: "",
        promptId: agent?.id || "",
        promptTitle: agent?.title || "",
        promptContent: agent?.tmplContent || agent?.content || "",
      };

      this._createAnnotationBase = {
        type: type,
        agent: agent,
        note: note,
        target: target,
        info: info,
      };

      if (!addType) {
        const newElement = document.createElement(`div-${baseId}`); // 创建空白标签
        newElement.innerHTML = `空白元素-${baseId}`;
        newElement.style.position = "absolute";
        newElement.style.opacity = "0";
        newElement.style.pointerEvents = "none";
        newElement.style.left = `${coordinates.left}px`;
        newElement.style.top = `${coordinates.top}px`;
        document.body.append(newElement);

        const range = document.createRange();
        range.selectNodeContents(newElement);
        const selection = window.getSelection();
        selection.removeAllRanges();
        selection.addRange(range);
      }
    } else {
      if (addType) {
        const rangeSelectors = await Promise.all(ranges.map((range) => this._integration.describe(root, range)));
        const relTarget: Target[] = rangeSelectors.map((selectors) => ({
          source: info.uri,
          selector: selectors,
        }));
        let arr = relTarget.concat(this._createAnnotationBase.target);
        this._createAnnotationBase.target = arr;
        const tagObject = JSON.parse(this._createAnnotationBase.note.tag);
        this._createAnnotationBase.note.tag = JSON.stringify({
          target: arr,
          coordinates: tagObject.coordinates,
          baseId,
        });
      }
    }

    if (addType && this._createAnnotationBase.type) {
      console.log("🔍 sending message with _createAnnotationBase:", this._createAnnotationBase);
      if (this._createAnnotationBase.type != "QUIZ" && this._createAnnotationBase.type != "AI") {
        console.log("🔍 sending addNoteNotice message");
        window.postMessage(
          {
            type: "addNoteNotice",
            note: this._createAnnotationBase.note,
            target: this._createAnnotationBase.target,
            info: this._createAnnotationBase.info,
            agent: this._createAnnotationBase.agent,
          },
          "*",
        );
      } else {
        console.log("🔍 sending setNoteNotice message");
        window.postMessage(
          {
            type: "setNoteNotice",
            note: {
              ...this._createAnnotationBase.note,
              ...{
                displayFlag: 1,
                id: Date.now(),
                type: this._createAnnotationBase.type,
              },
            },
            target: this._createAnnotationBase.target,
            info: this._createAnnotationBase.info,
          },
          "*",
        );
      }

      this._createAnnotationBase = {
        type: "",
        agent: null,
        note: null,
        target: null,
        info: null,
      };
    }
  }

  /**
   * 在侧边栏中指示某些注释被聚焦（即关联的文档区域被悬停）。
   */
  _hoverAnnotations(tags: string[]) {
    this._hoveredAnnotations.clear();
    tags.forEach((tag) => this._hoveredAnnotations.add(tag));
    for (const anchor of this.anchors) {
      if (anchor.highlights) {
        const toggle = tags.includes(anchor.annotation.$tag);
        setHighlightsFocused(anchor.highlights, toggle);
      }
    }
    // 便签hover效果
    this._adder.setNoteHover(tags);
  }

  /**
   * 当选择更改时显示或隐藏添加工具栏。
   */
  _onSelection(range: Range) {
    console.log("🔍 _onSelection called with range:", range, "text:", range.toString());
    // 首先检查是否是主页面的真实选择事件
    // 如果当前有iframe划词状态，说明这是主页面的新选择，需要清除iframe状态
    if ((this as any)._isIframeSelection) {
      // 清除iframe划词状态，为主页面划词做准备
      (this as any)._isIframeSelection = false;
      (this as any)._iframeSelectedText = null;
      (this as any)._iframeGlobalRect = null;

      // 隐藏之前的iframe工具栏
      if (this._isAdderVisible) {
        this._adder.hide();
        this._isAdderVisible = false;
      }
    }

    const annotatableRange = this._integration.getAnnotatableRange(range);
    console.log("🔍 annotatableRange:", annotatableRange);
    if (!annotatableRange) {
      console.log("🔍 no annotatable range, calling _onClearSelection");
      this._onClearSelection();
      return;
    }

    const selection = document.getSelection()!;
    const isBackwards = isSelectionBackwards(selection);
    let focusRect = selectionFocusRect(selection);

    if (!focusRect) {
      // 选定范围不包含任何文本
      this._onClearSelection();
      return;
    }

    // 对于主页面划词，实时计算当前的滚动偏移并调整focusRect
    // 注意：iframe划词不会进入这个方法，它们通过消息处理直接调用_adder.show()
    const iframeScrollAdjustment = this._getIframeScrollAdjustment();
    if (iframeScrollAdjustment.left !== 0 || iframeScrollAdjustment.top !== 0) {
      focusRect = new DOMRect(
        focusRect.left + iframeScrollAdjustment.left,
        focusRect.top + iframeScrollAdjustment.top,
        focusRect.width,
        focusRect.height,
      );
    }

    this.selectedRanges = [annotatableRange];
    this._hostRPC.call("textSelected");

    this._adder.annotationsForSelection = annotationsForSelection();
    this._isAdderVisible = true;

    this._range = range;

    // 主页面划词，更新选中的文本
    this._selectedText = range.toString();
    console.log("🔍 selected text:", this._selectedText);
    browser.storage.local.set({ selectedText: this._selectedText });
    if (this._selectedText.length <= 18 && this._selectedText.includes("空白元素")) {
      console.log("🔍 detected 空白元素, calling createAnnotation");
      const baseId = this._selectedText.replace("空白元素-", "");
      this.createAnnotation("PLAIN", null, true, baseId);
    } else {
      // console.log("划词选中")
      // 临时隐藏的网址
      const arr = sessionStorage.getItem("temporaryList") ? JSON.parse(sessionStorage.getItem("temporaryList")) : [];
      // 用户设置的需要隐藏的地址
      browser.storage.local.get(["smartMenu"]).then((result) => {
        if (result?.smartMenu) {
          let url = window.location.href.split("?")[0];
          if (
            (result.smartMenu?.isShowSmartMenu && !result.smartMenu.websiteList.includes(url) && !arr.includes(url)) ||
            Object.keys(result).length === 0
          ) {
            this._adder.show(focusRect, isBackwards);
          }
        } else {
          browser.storage.local.set({
            smartMenu: {
              isShowSmartMenu: true,
              userSwitch: {},
              websiteList: [],
            },
          });
          this._adder.show(focusRect, isBackwards);
        }
      });
    }
  }
  _onClearSelection() {
    this._isAdderVisible = false;
    this._adder.hide();
    this.selectedRanges = [];

    // 清除iframe划词标记
    (this as any)._isIframeSelection = false;
    (this as any)._iframeSelectedText = null;
    (this as any)._iframeGlobalRect = null;

    // 清除工具栏位置状态，确保下次划词时重新计算位置
    // 这进一步确保了多层iframe场景下的位置计算准确性
    (this._adder as any)._style = {};

    if (this._informHostOnNextSelectionClear) {
      this._hostRPC.call("textUnselected");
    }
    this._informHostOnNextSelectionClear = true;
  }

  /**
   * 在侧边栏中显示给定的注释。
   *
   * 这将在侧边栏中设置一个过滤器，以仅显示选定的注释，并打开侧边栏。可选地，它还可以将键盘焦点转移到第一个选定注释的注释卡上。
   *
   * @param tags
   * @param options
   *   @param [options.toggle] - 切换注释是否被选中，而不仅仅是选择它们
   */
  selectAnnotations(tags: string[]) {
    tags.forEach((id) => {
      this._adder.setNotelistByTag(id);
      this._hostRPC.call("setNoteShowClose", id, true);
    });
  }

  /**
   * 返回当前以悬停状态显示的注释标签。
   */
  get hoveredAnnotationTags(): Set<string> {
    return this._hoveredAnnotations;
  }

  /**
   * 获取iframe滚动偏移调整值
   * 用于修正多层嵌套iframe中的位置计算
   */
  private _getIframeScrollAdjustment(): { left: number; top: number } {
    // 如果不在iframe中，返回零偏移
    if (window === window.top) {
      return { left: 0, top: 0 };
    }

    let totalScrollLeft = 0;
    let totalScrollTop = 0;
    let currentWindow: Window = window;

    // 向上遍历所有iframe层级，累积滚动偏移
    while (currentWindow !== window.top && currentWindow.parent !== currentWindow) {
      try {
        // 获取当前窗口的滚动偏移
        const scrollLeft =
          (currentWindow as any).pageXOffset ||
          currentWindow.document.documentElement.scrollLeft ||
          currentWindow.document.body.scrollLeft ||
          0;
        const scrollTop =
          (currentWindow as any).pageYOffset ||
          currentWindow.document.documentElement.scrollTop ||
          currentWindow.document.body.scrollTop ||
          0;

        totalScrollLeft += scrollLeft;
        totalScrollTop += scrollTop;

        currentWindow = currentWindow.parent;
      } catch (e) {
        // 跨域限制，无法继续向上遍历
        break;
      }
    }

    return { left: totalScrollLeft, top: totalScrollTop };
  }
}
