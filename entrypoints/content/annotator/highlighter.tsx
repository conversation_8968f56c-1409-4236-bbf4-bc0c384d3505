import classnames from "classnames";
import { generateHexString } from "../shared/random";
import type { HighlightCluster } from "../types/shared";
import { isInPlaceholder } from "./anchoring/placeholder";
import { isNodeInRange } from "./range-util";

const SVG_NAMESPACE = "http://www.w3.org/2000/svg";

type HighlightProps = {
  // 关联的 SVG 矩形，用于表示此高亮（在 PDF 中）
  svgHighlight?: SVGRectElement;
};

export type HighlightElement = HTMLElement & HighlightProps;

export const clusterValues: HighlightCluster[] = ["user-annotations", "user-highlights", "other-content"];
/**
 * 返回 PDF 页面文本层中高亮元素下的画布元素。
 *
 * 如果高亮不在 PDF 画布上方，则返回 `null`。
 */
function getPDFCanvas(highlightEl: HighlightElement): HTMLCanvasElement | null {
  // 该代码假设 PDF.js 以如下结构渲染页面：
  //
  // <div class="page">
  //   <div class="canvasWrapper">
  //     <canvas></canvas> <!-- 渲染的 PDF 页面 -->
  //   </div>
  //   <div class="textLayer">
  //      <!-- 透明文本层，包含用于启用文本选择的文本跨度 -->
  //   </div>
  // </div>
  //
  // 它还假设 `highlightEl` 元素位于 `.textLayer` div 下的某个位置。

  const pageEl = highlightEl.closest(".page");
  if (!pageEl) {
    return null;
  }

  const canvasEl = pageEl.querySelector(".canvasWrapper > canvas");
  if (!canvasEl) {
    return null;
  }

  return canvasEl as HTMLCanvasElement;
}

/**
 * 在 PDF.js 画布上方的 SVG 层中绘制高亮。
 *
 * 创建的 SVG 元素存储在每个 `HighlightElement` 的 `svgHighlight` 属性中。
 *
 * @param highlightEls - 包含高亮文本的透明文本层中的元素
 * @param [cssClass] - 要添加到 SVG 高亮元素的 CSS 类
 */
function drawHighlightsAbovePDFCanvas(highlightEls: HighlightElement[], cssClass?: string) {
  if (highlightEls.length === 0) {
    return;
  }

  // 获取包含高亮的 PDF 页面画布。我们假设所有高亮都在同一页面上。
  const canvasEl = getPDFCanvas(highlightEls[0]);
  if (!canvasEl || !canvasEl.parentElement) {
    return;
  }

  let svgHighlightLayer = canvasEl.parentElement.querySelector(".hypothesis-highlight-layer") as SVGSVGElement | null;

  if (!svgHighlightLayer) {
    // 创建 SVG 层。它必须与画布处于相同的堆叠上下文中，以便使用 CSS `mix-blend-mode` 控制 SVG 内容与下方画布的混合方式。
    svgHighlightLayer = document.createElementNS(SVG_NAMESPACE, "svg");
    svgHighlightLayer.setAttribute("class", "hypothesis-highlight-layer");
    canvasEl.parentElement.appendChild(svgHighlightLayer);

    // 在画布上方覆盖 SVG 层。
    canvasEl.parentElement.style.position = "relative";

    const svgStyle = svgHighlightLayer.style;
    svgStyle.position = "absolute";
    svgStyle.left = "0";
    svgStyle.top = "0";
    svgStyle.width = "100%";
    svgStyle.height = "100%";

    // 使用乘法混合模式，使绘制在文本上的高亮变暗而不是变亮。这提高了高亮文本的对比度和可读性，特别是对于重叠的高亮。
    //
    // 这种选择优化了深色文本在浅色背景上的常见情况。
    svgStyle.mixBlendMode = "multiply";
  }

  const canvasRect = canvasEl.getBoundingClientRect();
  const highlightRects = highlightEls.map((highlightEl) => {
    const highlightRect = highlightEl.getBoundingClientRect();

    // 为当前高亮元素创建 SVG 元素。
    const rect = document.createElementNS(SVG_NAMESPACE, "rect");
    rect.setAttribute("x", (highlightRect.left - canvasRect.left).toString());
    rect.setAttribute("y", (highlightRect.top - canvasRect.top).toString());
    rect.setAttribute("width", highlightRect.width.toString());
    rect.setAttribute("height", highlightRect.height.toString());
    rect.setAttribute("class", classnames("hypothesis-svg-highlight", cssClass));

    // 使文本层中的高亮透明。
    highlightEl.classList.add("is-transparent");

    // 将 SVG 元素与高亮关联，以便 `removeHighlights` 使用。
    highlightEl.svgHighlight = rect;

    return rect;
  });

  svgHighlightLayer.append(...highlightRects);
}

/**
 * 返回完全在 `range` 内的文本节点。
 *
 * 如果范围在文本节点中间开始或结束，则节点会被拆分，返回范围内的部分。
 */
export function wholeTextNodesInRange(range: Range): Text[] {
  if (range.collapsed) {
    // 提前退出空范围，以避免破坏下面算法的边缘情况。
    // 在空范围的起始位置拆分文本节点可能会导致范围结束在左侧部分而不是右侧部分。
    return [];
  }

  let root = range.commonAncestorContainer as Node | null;
  if (root && root.nodeType !== Node.ELEMENT_NODE) {
    // 如果公共祖先不是元素，则将其设置为父元素，以确保下面的循环访问拆分公共祖先生成的任何文本节点。
    //
    // 注意，`parentElement` 可能为 `null`。
    root = root.parentElement;
  }
  if (!root) {
    // 如果没有根元素，则无法插入高亮，因此在此退出。
    return [];
  }

  const textNodes = [];
  const nodeIter = root!.ownerDocument!.createNodeIterator(
    root,
    NodeFilter.SHOW_TEXT, // 仅返回 `Text` 节点。
  );
  let node;
  while ((node = nodeIter.nextNode())) {
    if (!isNodeInRange(range, node)) {
      continue;
    }
    const text = node as Text;

    if (text === range.startContainer && range.startOffset > 0) {
      // 在范围开始处拆分 `text`。拆分将创建一个新的 `Text` 节点，该节点将在范围内，并将在下一次循环迭代中访问。
      text.splitText(range.startOffset);
      continue;
    }

    if (text === range.endContainer && range.endOffset < text.data.length) {
      // 在范围结束处拆分 `text`，将其保留为范围内的部分。
      text.splitText(range.endOffset);
    }

    textNodes.push(text);
  }

  return textNodes;
}

/**
 * 将指定范围内的 DOM 节点用高亮元素包裹起来，并返回这些高亮元素。
 *
 * @param range - 要高亮的范围
 * @param [cssClass] - 要添加到高亮元素的 CSS 类
 * @return 包裹 `normedRange` 中文本的元素，以添加高亮效果
 */
export function highlightRange(range: Range, cssClass?: string, id?: string): HighlightElement[] {
  console.log(range, 222);
  const textNodes = wholeTextNodesInRange(range);

  // 检查此范围是否引用 PDF 中尚未渲染内容的占位符。这些高亮应不可见。
  const inPlaceholder = textNodes.length > 0 && isInPlaceholder(textNodes[0]);

  // 将文本节点分组为相邻节点的跨度。如果一组文本节点是相邻的，我们只需要为该组创建一个高亮元素。
  let textNodeSpans: Text[][] = [];
  let prevNode: Node | null = null;
  let currentSpan = null;

  textNodes.forEach((node) => {
    if (prevNode && prevNode.nextSibling === node) {
      currentSpan.push(node);
    } else {
      currentSpan = [node];
      textNodeSpans.push(currentSpan);
    }
    prevNode = node;
  });

  // 过滤掉仅包含空白的文本节点跨度。这避免在只能包含受限节点子集的地方插入高亮，例如表格行和列表。
  const whitespace = /^\s*$/;
  textNodeSpans = textNodeSpans.filter((span) => {
    const parentElement = span[0].parentElement;
    return (
      // 空白 <span> 应该被高亮，因为它们会影响某些代码编辑器中的布局
      (parentElement?.childNodes.length === 1 && parentElement?.tagName === "SPAN") ||
      // 否则忽略仅包含空白的文本节点跨度
      span.some((node) => !whitespace.test(node.data))
    );
  });

  // 用 `<hypothesis-highlight>` 元素包裹每个文本节点跨度。
  const highlights: HighlightElement[] = [];

  textNodeSpans.forEach((nodes) => {
    // 这里使用自定义元素名称而不是 `<span>`，以减少页面样式隐藏高亮的可能性。

    const highlightEl = document.createElement("hypothesis-highlight");
    highlightEl.className = classnames("hypothesis-highlight", cssClass);

    highlightEl.setAttribute("id", /^空白元素-\d{13}$/.test(nodes[0]?.nodeValue) ? id : `base-${id}`);
    const parent = nodes[0].parentNode as ParentNode;
    parent.replaceChild(highlightEl, nodes[0]);
    nodes.forEach((node) => highlightEl.appendChild(node));
    highlights.push(highlightEl);
  });

  // 对于 PDF 高亮，通过在页面画布上方使用 SVG 创建高亮效果，而不是在高亮元素上使用 CSS `background-color`。这使得可以更好地控制高亮与下方内容的混合。
  //
  // 绘制这些 SVG 高亮涉及测量 `<hypothesis-highlight>` 元素，因此我们在创建所有这些元素后才创建它们，以减少强制重排的次数。我们还跳过为未渲染的页面创建它们，以提高性能。
  if (!inPlaceholder) {
    drawHighlightsAbovePDFCanvas(highlights, cssClass);
  }

  return highlights;
}

/**
 * 用 `replacements` 替换子节点 `node`。
 *
 * 注意：这类似于 `ChildNode.replaceWith`，但适用于旧版浏览器。
 */
function replaceWith(node: ChildNode, replacements: Node[]) {
  const parent = node.parentNode as ParentNode;
  replacements.forEach((r) => parent.insertBefore(r, node));
  node.remove();
}

/**
 * 移除给定根元素下的所有高亮。
 */
export function removeAllHighlights(root: HTMLElement) {
  const highlights = Array.from(root.querySelectorAll("hypothesis-highlight"));
  removeHighlights(highlights as HighlightElement[]);
}

/**
 * 移除先前使用 `highlightRange` 高亮的范围中的高亮。
 */
export function removeHighlights(highlights: HighlightElement[]) {
  // 显式取消焦点高亮以移除。这确保关联的焦点元素从文档中移除。
  setHighlightsFocused(highlights, false);
  for (const h of highlights) {
    if (h.parentNode) {
      const children = Array.from(h.childNodes);
      replaceWith(h, children);
    }
    if (h.svgHighlight) {
      h.svgHighlight.remove();
    }
  }
}

/**
 * 聚焦或取消聚焦单个 SVG 高亮元素。
 *
 * 聚焦 SVG 高亮时，确保它不会被其他 SVG 高亮元素遮挡。由于 SVG 高亮是兄弟元素，可以通过将高亮放在其父级的高亮集末尾来实现。这些元素被克隆而不是移动，以便在取消聚焦时不会丢失其原始堆叠顺序。添加数据属性以将原始 SVG 高亮元素与其克隆关联。
 */
function setSVGHighlightFocused(svgEl: SVGElement, focused: boolean) {
  const parent = svgEl.parentNode as SVGElement;
  // 该属性允许查找关联的“聚焦”元素。如果高亮已聚焦，则设置。
  const focusedId = svgEl.getAttribute("data-focused-id");

  const isFocused = Boolean(focusedId);
  if (isFocused === focused) {
    return;
  }

  if (focused) {
    svgEl.setAttribute("data-focused-id", generateHexString(8));
    const focusedHighlight = svgEl.cloneNode() as SVGElement;
    // 克隆的元素将包含 `data-focused-id` 属性，以便与其原始高亮关联。设置额外属性以标记这是高亮的聚焦克隆。
    focusedHighlight.setAttribute("data-is-focused", "data-is-focused");
    parent.append(focusedHighlight);
  } else {
    const focusedHighlight = parent.querySelector(`[data-focused-id="${focusedId}"][data-is-focused]`);
    focusedHighlight?.remove();
    svgEl.removeAttribute("data-focused-id");
  }
}

/**
 * 设置给定高亮元素是否应显示为“聚焦”。
 *
 * 高亮可以以不同的（“聚焦”）样式显示，以指示它在其他上下文中是当前的 - 例如用户在侧边栏中选择了相应的注释。
 */
export function setHighlightsFocused(highlights: HighlightElement[], focused: boolean) {
  highlights.forEach((h) => {
    // 在 PDF 中，可见的高亮是由 SVG 元素创建的，因此聚焦效果应用于该元素。在其他文档中，效果应用于 `<hypothesis-highlight>` 元素。
    if (h.svgHighlight) {
      setSVGHighlightFocused(h.svgHighlight, focused);
    } else {
      h.classList.toggle("hypothesis-highlight-focused", focused);
    }
  });
}

/**
 * 获取包含给定节点的高亮元素。
 */
export function getHighlightsContainingNode(node: Node): HighlightElement[] {
  let el = node.nodeType === Node.ELEMENT_NODE ? (node as Element) : node.parentElement;

  const highlights = [];

  while (el) {
    if (el.classList.contains("hypothesis-highlight")) {
      highlights.push(el);
    }
    el = el.parentElement;
  }

  return highlights as HighlightElement[];
}

// `DOMRect` 接口的子集
type Rect = {
  top: number;
  left: number;
  bottom: number;
  right: number;
};

/**
 * 获取集合在视口坐标中的边界客户端矩形。
 * 不幸的是，Chrome 在 Range.getBoundingClientRect 上有问题（[1]），否则我们可以直接使用它。
 *
 * [1] https://bugs.chromium.org/p/chromium/issues/detail?id=324437
 */
export function getBoundingClientRect(collection: HTMLElement[]): Rect {
  // 将高亮的客户端矩形减少到一个边界框
  const rects = collection.map((n) => n.getBoundingClientRect() as Rect);
  return rects.reduce((acc, r) => ({
    top: Math.min(acc.top, r.top),
    left: Math.min(acc.left, r.left),
    bottom: Math.max(acc.bottom, r.bottom),
    right: Math.max(acc.right, r.right),
  }));
}

/**
 * `el` 是否为高亮元素？解决 HTML 文档（`tagName` 为大写）和 XHTML 文档（`tagName` 为小写）之间的不一致性
 */
const isHighlightElement = (el: Element): boolean => el.tagName.toLowerCase() === "hypothesis-highlight";

/**
 * 获取 `el` 的高亮嵌套级别。这通常通过高亮元素上的 `data-nesting-level` 属性设置。聚焦的 SVG 高亮元素应始终具有最高的嵌套级别——它们在排序时应始终排在最后，以免被其他高亮遮挡。这些元素通过存在 `data-is-focused` 属性来指示。
 */
function nestingLevel(el: Element): number {
  if (el.getAttribute("data-is-focused")) {
    return Number.MAX_SAFE_INTEGER;
  }
  return parseInt(el.getAttribute("data-nesting-level") ?? "0", 10);
}
