import "@/assets/styles/content.less";
import "@/assets/styles/highlights.less";
import createSidePanelWrapper from "./sidepanel";
import { v4 as uuidv4 } from "uuid";
import { createNote } from "@/utils/notes";
// import FormFillingWrapper from "./formFilling";
// import FormDetectorWrapper from "./FormDetectorWrapper";

import ReactDOM from "react-dom/client";

export default defineContentScript({
  matches: ["<all_urls>"],
  runAt: "document_end",
  main() {
    ////////// 以下是作用域全局参数
    let loadingElement: HTMLElement | null = null;
    let observer: MutationObserver | null = null;
    let loadingInserted = false; // 添加标志位防止重复插入

    function insertEarlyLoading() {
      // 防止重复插入
      if (loadingInserted || document.getElementById("ai__plugin-global-loading")) {
        return;
      }

      loadingInserted = true; // 设置标志位

      const tryInsert = () => {
        const head = document.head;
        const body = document.body;
        if (!head || !body) {
          requestAnimationFrame(tryInsert);
          return;
        }

        // 检查是否已经存在（防止重复）
        if (document.getElementById("ai__plugin-global-loading")) {
          return;
        }

        // 样式
        const style = document.createElement("style");
        style.id = "__plugin-global-loading-style";
        style.textContent = `
          #ai__plugin-global-loading {
            position: fixed;
            top:0; left:0; right:0; bottom:0;
            display:flex; flex-direction:column;
            align-items:center; justify-content:center;
            background: rgba(0,0,0,0.4);
            z-index: 999999;
          }
          .loading-container {
            position: relative;
            width: 200px;
            height: 200px;
            display:flex;
            justify-content:center;
            align-items:center;
          }
          .ai-text {
            font-family: Arial, sans-serif;
            font-size: 70px;
            font-weight: 800;
            color: rgba(255,255,255,0.9);
            position: relative;
            z-index: 10;
            letter-spacing: -2px;
            background: linear-gradient(45deg,#00c6ff,#0072ff);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            text-shadow: 0 0 15px rgba(0,198,255,0.5);
            animation: pulse 3s ease-in-out infinite;
          }
          @keyframes pulse {
            0%,100%{filter:brightness(1);}
            50%{filter:brightness(1.3);}
          }
          .circle { position: absolute; border-radius:50%; border:2px solid rgba(0,198,255,0.2); border-top:2px solid #00c6ff; border-left:2px solid #00c6ff;}
          .circle-1 { width:120px; height:120px; animation: spin 2s linear infinite; }
          .circle-2 { width:150px; height:150px; animation: spin 4s linear infinite reverse; }
          .circle-3 { width:180px; height:180px; animation: spin 6s linear infinite; }
          @keyframes spin {0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}
          .particles { position:absolute; top:0; left:0; width:100%; height:100%; }
          .particle { position:absolute; width:3px; height:3px; background:#00c6ff; border-radius:50%; }
          .grid { position:absolute; width:200px; height:200px; border-radius:50%; overflow:hidden; opacity:0.3; }
          .grid::before,
          .grid::after { content:''; position:absolute; top:0; left:0; width:200px; height:200px; }
          .grid::before { background: linear-gradient(90deg, transparent 29px, #00c6ff 30px, #00c6ff 31px, transparent 32px); background-size: 60px 100%; }
          .grid::after { background: linear-gradient(0deg, transparent 29px, #00c6ff 30px, #00c6ff 31px, transparent 32px); background-size:100% 60px; }
        `;
        head.appendChild(style);

        // loading DOM
        loadingElement = document.createElement("div");
        loadingElement.id = "ai__plugin-global-loading";

        const loadingContainer = document.createElement("div");
        loadingContainer.className = "loading-container";

        const grid = document.createElement("div");
        grid.className = "grid";

        const circle1 = document.createElement("div");
        circle1.className = "circle circle-1";
        const circle2 = document.createElement("div");
        circle2.className = "circle circle-2";
        const circle3 = document.createElement("div");
        circle3.className = "circle circle-3";

        const aiText = document.createElement("div");
        aiText.className = "ai-text";
        aiText.textContent = "AI";

        const particles = document.createElement("div");
        particles.className = "particles";

        loadingContainer.append(grid, circle1, circle2, circle3, aiText, particles);
        loadingElement.appendChild(loadingContainer);
        body.appendChild(loadingElement);

        // 粒子动画
        const numParticles = 15;
        for (let i = 0; i < numParticles; i++) createParticle();

        function createParticle() {
          const particle = document.createElement("div");
          particle.classList.add("particle");

          const angle = Math.random() * Math.PI * 2;
          const distance = Math.random() * 80 + 20;
          const x = 100 + Math.cos(angle) * distance;
          const y = 100 + Math.sin(angle) * distance;
          particle.style.left = `${x}px`;
          particle.style.top = `${y}px`;
          const size = Math.random() * 2 + 1;
          particle.style.width = `${size}px`;
          particle.style.height = `${size}px`;
          particle.style.opacity = Math.random() * 0.5 + 0.3;

          particles.appendChild(particle);
          animateParticle(particle, x, y);
        }

        function animateParticle(particle: HTMLElement, startX: number, startY: number) {
          const maxMove = 15;
          let currentX = startX;
          let currentY = startY;
          let targetX = startX + (Math.random() * maxMove * 2 - maxMove);
          let targetY = startY + (Math.random() * maxMove * 2 - maxMove);
          let progress = 0;
          const speed = 0.005 + Math.random() * 0.005;

          function animate() {
            progress += speed;
            if (progress >= 1) {
              progress = 0;
              currentX = targetX;
              currentY = targetY;
              targetX = startX + (Math.random() * maxMove * 2 - maxMove);
              targetY = startY + (Math.random() * maxMove * 2 - maxMove);
            }
            const x = currentX + (targetX - currentX) * progress;
            const y = currentY + (targetY - currentY) * progress;
            particle.style.left = `${x}px`;
            particle.style.top = `${y}px`;
            requestAnimationFrame(animate);
          }
          animate();
        }

        // 监控 body 防止被覆盖
        observer = new MutationObserver(() => {
          if (!document.getElementById("ai__plugin-global-loading")) {
            body.appendChild(loadingElement!);
          }
        });
        observer.observe(body, { childList: true });
      };
      tryInsert();
    }

    function removeLoading() {
      if (observer) {
        observer.disconnect();
        observer = null;
      }
      if (loadingElement && loadingElement.parentNode) {
        loadingElement.parentNode.removeChild(loadingElement);
        loadingElement = null;
      }
      const style = document.getElementById("__plugin-global-loading-style");
      if (style) style.remove();
      loadingInserted = false; // 重置标志位
    }

    // 插入 loading - 只调用一次
    insertEarlyLoading();

    // 页面加载完成再移除
    window.addEventListener("load", () => {
      setTimeout(removeLoading, 100);
    });

    /////// 以下保持你原有逻辑 ///////
    const style = document.createElement("style");
    style.innerHTML = `
      .hypothesis-highlight {
        background-color: rgba(156, 230, 255, 0.5);
        &.is-transparent {
          background-color: rgb(208, 218, 121) !important;
          color: inherit !important;
        }
        &.hypothesis-highlight-focused {
          background-color: #ffec3d;
        }
      }
    `;
    document.head.appendChild(style);

    let noteMouseX: number;
    let noteMouseY: number;

    document.addEventListener("contextmenu", (event) => {
      noteMouseX = event.clientX;
      noteMouseY = (document.documentElement.scrollTop || document.body.scrollTop) + event.clientY;
    });

    sessionStorage.setItem("sino-tap-key", uuidv4());

    // 顶层 iframe 逻辑
    // content.tsx - 仅修改 message 事件处理部分
    // 顶层 iframe 逻辑
    if (window.top === window) {
      let guestInstance: any = null;
      (window as any).setGuestInstance = (guest: any) => {
        guestInstance = guest;
        // ✅ 全局绑定一次
        if (guestInstance && typeof guestInstance._onCreateNote === "function") {
          guestInstance._adder.onCreate = () => {
            console.log("📝 Create clicked with text:", guestInstance._iframeSelectedText);
            guestInstance._onCreateNote(guestInstance._iframeSelectedText, guestInstance._iframeGlobalRect);
          };
        }
      };

      /** 递归查找包含目标窗口的 iframe（支持 Shadow DOM） */
      function findIframeContainingWindow(
        targetWindow: Window,
        root: Document | ShadowRoot = document,
      ): HTMLIFrameElement | null {
        // 检查普通 iframe
        const iframes = root.querySelectorAll("iframe");
        for (const iframe of iframes) {
          try {
            if (iframe.contentWindow === targetWindow) {
              return iframe;
            }
          } catch (e) {
            // 跨域情况，尝试通过其他方式匹配
            if (targetWindow.location && iframe.src) {
              try {
                const iframeUrl = new URL(iframe.src);
                const targetUrl = new URL(targetWindow.location.href);
                if (iframeUrl.hostname === targetUrl.hostname) {
                  return iframe;
                }
              } catch (urlError) {
                // URL 解析失败，跳过
              }
            }
          }
        }

        // 递归检查 Shadow DOM
        const shadowHosts = root.querySelectorAll("*");
        for (const host of shadowHosts) {
          if (host.shadowRoot) {
            const found = findIframeContainingWindow(targetWindow, host.shadowRoot);
            if (found) return found;
          }
        }

        return null;
      }

      /** 获取 iframe 在文档中的位置（支持 Shadow DOM） */
      function getIframePosition(iframe: HTMLIFrameElement): { top: number; left: number } {
        try {
          // 对于 Shadow DOM 中的 iframe，需要累积偏移量
          function getCumulativeOffset(element: Element): { top: number; left: number } {
            let top = 0;
            let left = 0;
            let current = element;

            while (current) {
              try {
                const rect = current.getBoundingClientRect();
                top += rect.top;
                left += rect.left;

                // 检查是否在 Shadow DOM 中
                const root = current.getRootNode();
                if (root instanceof ShadowRoot) {
                  current = root.host;
                } else {
                  break;
                }
              } catch (e) {
                console.debug("⚠️ 获取元素位置失败", e);
                break;
              }
            }

            return { top, left };
          }

          return getCumulativeOffset(iframe);
        } catch (e) {
          console.debug("⚠️ 无法获取 iframe 位置", e);
          return { top: 0, left: 0 };
        }
      }

      /** 检查累积偏移量是否已经包含了最外层距离 */
      function hasOutermostOffset(cumulativeOffset: { top: number; left: number }, sourceWindow: Window): boolean {
        try {
          const outermostIframe = findIframeContainingWindow(sourceWindow);
          if (outermostIframe) {
            const rect = outermostIframe.getBoundingClientRect();
            // 如果累积偏移量已经接近最外层iframe的位置，说明已经包含
            return Math.abs(cumulativeOffset.top - rect.top) < 5 && Math.abs(cumulativeOffset.left - rect.left) < 5;
          }
        } catch (e) {
          console.debug("⚠️ 检查最外层偏移失败", e);
        }
        return false;
      }

      /** 尝试找到源窗口对应的最外层 iframe 并计算其到窗口的距离 */
      function getOutermostIframeOffset(sourceWindow: Window): { top: number; left: number } {
        try {
          const outermostIframe = findIframeContainingWindow(sourceWindow);
          if (outermostIframe) {
            const offset = getIframePosition(outermostIframe);
            console.log("📍 找到最外层iframe到窗口距离:", offset);
            return offset;
          }
        } catch (e) {
          console.debug("⚠️ 查找最外层iframe失败", e);
        }
        return { top: 0, left: 0 };
      }

      window.addEventListener("message", (e: MessageEvent) => {
        if (!e.data || (e.data.type !== "iframe-selection" && e.data.type !== "iframe-mousedown")) return;
        if ((e.data as any)._handled) return;
        (e.data as any)._handled = true;

        let globalRect: DOMRect | undefined;
        if (e.data.type === "iframe-selection") {
          const srcWin = e.source as Window;
          const rect = e.data.rect;
          const text = e.data.text;
          let cumulativeOffset = e.data.cumulativeOffset || { top: 0, left: 0 };

          console.log("📊 收到累积偏移量:", cumulativeOffset);
          console.log("🌳 iframe 路径:", e.data.iframePath);

          // 关键修复：只在需要时添加最外层距离
          const outermostOffset = getOutermostIframeOffset(srcWin);
          const alreadyHasOutermostOffset = hasOutermostOffset(cumulativeOffset, srcWin);

          if (!alreadyHasOutermostOffset && (outermostOffset.top !== 0 || outermostOffset.left !== 0)) {
            console.log("➕ 添加最外层iframe到窗口距离:", outermostOffset);
            cumulativeOffset.top += outermostOffset.top;
            cumulativeOffset.left += outermostOffset.left;
          } else {
            console.log("✅ 累积偏移量已包含最外层距离");
          }

          // 添加页面滚动偏移
          const scrollAdjustedOffset = {
            top: cumulativeOffset.top + window.scrollY,
            left: cumulativeOffset.left + window.scrollX,
          };

          globalRect = new DOMRect(
            rect.left + scrollAdjustedOffset.left,
            rect.top + scrollAdjustedOffset.top,
            rect.width,
            rect.height,
          );

          browser.storage.local.set({ selectedText: text });
          guestInstance._onClearSelection();
          guestInstance._isIframeSelection = true;
          guestInstance._iframeSelectedText = text;
          guestInstance._iframeGlobalRect = globalRect;
          guestInstance._isAdderVisible = true;
          guestInstance._adder.show(globalRect, false);
          if (guestInstance && typeof guestInstance._onCreateNote === "function") {
            guestInstance._adder.onCreate = () => {
              console.log("📝 Create clicked with text:", guestInstance._iframeSelectedText);
              guestInstance._onCreateNote(guestInstance._iframeSelectedText, guestInstance._iframeGlobalRect);
            };
          }
        }
        if (e.data.type === "iframe-mousedown" && guestInstance) guestInstance._onClearSelection();
        console.log("✅ 最终 iframe 绝对选区:", globalRect);
      });
    }
    /** 注册window加载完成事件 */
    window.addEventListener("load", async () => {
      createSidePanelWrapper();
      // 这里不要再次调用 removeLoading()，因为上面已经有 load 事件监听器了
      setInterval(() => {
        browser.runtime.sendMessage({ type: "wakeUp" });
      }, 6000000);
    });

    window.addEventListener("message", (event) => {
      if (event.source !== window) return;
      if (event.data?.type === "chatKnowInfo") {
        browser.runtime.sendMessage({ type: "chatKnowInfo", data: event.data });
      }
    });

    browser.runtime.onMessage.addListener(async (message) => {
      console.log(message, 2333);
      switch (message.type) {
        case "createNote":
          createNote(message, noteMouseX, noteMouseY);
          break;
        default:
          break;
      }
    });

    // 表单检测面板
    // const formDetectorShadowDom = document.createElement("shadow-dom");
    // formDetectorShadowDom.id = "shadow-form-detector";
    // const formDetectorShadowRoot = formDetectorShadowDom.attachShadow({ mode: "open" });

    // const formDetectorCSS = document.createElement("link");
    // formDetectorCSS.rel = "stylesheet";
    // formDetectorCSS.href = chrome.runtime.getURL("content-scripts/content.css");
    // formDetectorShadowRoot.appendChild(formDetectorCSS);

    // const formDetectorInlineStyle = document.createElement("style");
    // formDetectorInlineStyle.textContent = `
    //   #form-detector-container { position: fixed; top:0; left:0; width:100vw; height:100vh; pointer-events:none; z-index:10000; }
    //   #form-detector-container .form-detector-panel, #form-detector-container .data-collector-panel { pointer-events:auto; }
    // `;
    // formDetectorShadowRoot.appendChild(formDetectorInlineStyle);

    // const formDetectorContainer = document.createElement("div");
    // formDetectorContainer.id = "form-detector-container";
    // formDetectorShadowRoot.appendChild(formDetectorContainer);

    // ReactDOM.createRoot(formDetectorContainer).render(
    //   <FormDetectorWrapper formDetectorShadowRoot={formDetectorShadowRoot} />,
    // );
    // document.body.appendChild(formDetectorShadowDom);
  },
});
