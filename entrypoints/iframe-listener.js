import { defineContentScript } from "wxt/sandbox";

export default defineContentScript({
  matches: ["<all_urls>"],
  runAt: "document_idle",
  allFrames: true,
  main(ctx) {
    // 标记消息来源，防止循环
    const SCRIPT_ID = Math.random().toString(36).substr(2, 9);

    console.log(`🔍 iframe 内容脚本加载 (${SCRIPT_ID})`, {
      href: window.location.href,
      isTop: window === window.top,
      parentEqualsTop: window.parent === window.top
    });

    // 所有 iframe 都执行（包括中间层）
    if (window.top !== window) {
      /** 递归查找当前 iframe 元素（支持 Shadow DOM） */
      function findCurrentIframe(parentDoc, targetWindow) {
        try {
          // 检查普通 iframe
          const iframes = parentDoc.querySelectorAll("iframe");
          for (let i = 0; i < iframes.length; i++) {
            try {
              if (iframes[i].contentWindow === targetWindow) {
                return iframes[i];
              }
            } catch (e) {
              // 跨域情况，尝试通过其他方式匹配
              if (targetWindow.location && iframes[i].src) {
                try {
                  const iframeUrl = new URL(iframes[i].src);
                  const targetUrl = new URL(targetWindow.location.href);
                  if (iframeUrl.hostname === targetUrl.hostname) {
                    return iframes[i];
                  }
                } catch (urlError) {
                  // URL 解析失败，跳过
                }
              }
            }
          }

          // 递归检查 Shadow DOM
          const shadowHosts = parentDoc.querySelectorAll('*');
          for (let i = 0; i < shadowHosts.length; i++) {
            const host = shadowHosts[i];
            if (host.shadowRoot) {
              const found = findCurrentIframe(host.shadowRoot, targetWindow);
              if (found) return found;
            }
          }
        } catch (error) {
          console.debug("⚠️ 查找 iframe 失败:", error);
        }

        return null;
      }

      /** 找到当前 iframe 在父窗口中的偏移（保留，虽然我们发起时不直接累积） */
      function getCurrentIframeOffset() {
        try {
          const iframe = findCurrentIframe(window.parent.document, window);
          if (iframe) {
            const rect = iframe.getBoundingClientRect();
            return { top: rect.top, left: rect.left };
          }
        } catch (e) {
          console.debug("⚠️ 跨域 iframe，无法获取父级 offset");
        }
        return { top: 0, left: 0 };
      }

      /** （保留）是否需要累积 - 现保留但不强制使用（以便未来调整） */
      function checkIfAccumulationNeeded(iframe, existingOffset) {
        try {
          const iframeRect = iframe.getBoundingClientRect();

          if (Math.abs(iframeRect.top) < 5 && Math.abs(iframeRect.left) < 5 &&
            Math.abs(existingOffset.top) < 5 && Math.abs(existingOffset.left) < 5) {
            console.log("🚫 跳过累积：iframe和现有偏移量都接近0");
            return false;
          }

          const viewportHeight = window.innerHeight;
          const viewportWidth = window.innerWidth;

          if (existingOffset.top > viewportHeight * 0.3 || existingOffset.left > viewportWidth * 0.3) {
            console.log("🚫 跳过累积：现有偏移量过大，可能已过度累积");
            return false;
          }

          const isIframeInViewport =
            iframeRect.top >= 0 && iframeRect.left >= 0 &&
            iframeRect.bottom <= viewportHeight && iframeRect.right <= viewportWidth;

          if (!isIframeInViewport) {
            console.log("✅ 需要累积：iframe在视口外");
            return true;
          }

          const hasSignificantPosition = Math.abs(iframeRect.top) > 10 || Math.abs(iframeRect.left) > 10;
          console.log(`📊 累积判断：iframe位置(top:${iframeRect.top}, left:${iframeRect.left})，显著位置:${hasSignificantPosition}`);

          return hasSignificantPosition;
        } catch (e) {
          console.debug("⚠️ 累积检查失败", e);
          return true;
        }
      }

      /** pointerup -> 发送划词消息
       *  关键：**不在发起端重复加入本层的 iframe rect**，将 cumulativeOffset 从 0 开始
       */
      function handlePointerUp() {
        setTimeout(() => {
          const selection = window.getSelection();
          if (!selection || selection.isCollapsed) return;

          const text = selection.toString().trim();
          if (!text) return;

          const range = selection.getRangeAt(0);
          const rect = range.getBoundingClientRect();

          // 关键改动：发起端把 cumulativeOffset 初始化为 0（由父层逐级累加）
          const messageData = {
            type: "iframe-selection",
            text,
            rect: {
              top: rect.top,
              left: rect.left,
              width: rect.width,
              height: rect.height,
            },
            cumulativeOffset: { top: 0, left: 0 }, // <- 不预先累加本层 iframe rect
            sourceHref: window.location.href,
            isOutermost: window.parent === window.top,
            scriptId: SCRIPT_ID,
            timestamp: Date.now(),
            _origin: 'pointer'
          };

          console.log(`🚀 [${SCRIPT_ID}] 发送划词消息:`, {
            text: text.substring(0, 50) + (text.length > 50 ? '...' : ''),
            rect: messageData.rect,
            offset: messageData.cumulativeOffset
          });

          window.parent.postMessage(messageData, "*");
        }, 10);
      }

      /** pointerdown -> 通知父级清空 */
      function handlePointerDown() {
        window.parent.postMessage({
          type: "iframe-mousedown",
          sourceHref: window.location.href,
          scriptId: SCRIPT_ID,
          timestamp: Date.now()
        }, "*");
      }

      /** 处理来自子 iframe 的消息 */
      function handleMessage(e) {
        // 基础验证
        if (!e.data || typeof e.data !== 'object') return;
        if (e.data.type !== "iframe-selection" && e.data.type !== "iframe-mousedown") return;

        // 防止消息循环
        if (e.data.scriptId === SCRIPT_ID) {
          console.log(`🔄 [${SCRIPT_ID}] 忽略自己的消息`);
          return;
        }

        if (e.data._handledBy && e.data._handledBy.includes(SCRIPT_ID)) {
          console.log(`🔄 [${SCRIPT_ID}] 消息已处理过`);
          return;
        }

        console.log(`📨 [${SCRIPT_ID}] 收到消息:`, {
          type: e.data.type,
          source: e.source !== window ? 'child' : 'unknown',
          origin: e.data._origin || 'unknown'
        });

        // 标记消息来源
        const messageData = {
          ...e.data,
          _handledBy: [...(e.data._handledBy || []), SCRIPT_ID],
          _forwardCount: (e.data._forwardCount || 0) + 1
        };

        // 关键修改：由当前层**为发送者（e.source）在本层文档中的 iframe 元素位置**进行累加
        if (messageData.type === "iframe-selection" && messageData.rect && messageData.cumulativeOffset) {
          let cumulativeOffset = messageData.cumulativeOffset;

          try {
            // 找到触发消息的 iframe（该 iframe 在当前文档中即为 child）
            const childIframe = findCurrentIframe(document, e.source);
            if (childIframe) {
              // 直接累加 childIframe 的 rect（即 child iframe 在当前文档中的位置）
              const rect = childIframe.getBoundingClientRect();
              cumulativeOffset = {
                top: (cumulativeOffset.top || 0) + rect.top,
                left: (cumulativeOffset.left || 0) + rect.left,
              };
              console.log(`📐 [${SCRIPT_ID}] 累积偏移量 (加上 child iframe 在本层的位置):`, cumulativeOffset);
            } else {
              console.log(`ℹ️ [${SCRIPT_ID}] 未找到 child iframe（可能跨域或结构问题），跳过本层累加`);
            }
          } catch (err) {
            console.debug(`⚠️ [${SCRIPT_ID}] 跨域或错误，跳过本层 offset 累积：`, err);
          }

          messageData.cumulativeOffset = cumulativeOffset;
        }

        // 转发到父窗口（或到顶层）
        if (window.parent !== window) {
          console.log(`🔄 [${SCRIPT_ID}] 转发消息到父级 (${messageData._forwardCount}层)`);
          window.parent.postMessage(messageData, "*");
        } else {
          console.log(`🎯 [${SCRIPT_ID}] 到达顶层，等待顶层处理`);
        }
      }

      // 绑定事件监听器
      document.addEventListener("pointerup", handlePointerUp);
      document.addEventListener("pointerdown", handlePointerDown);
      window.addEventListener("message", handleMessage);

      // 清理函数
      ctx.onInvalidated(() => {
        document.removeEventListener("pointerup", handlePointerUp);
        document.removeEventListener("pointerdown", handlePointerDown);
        window.removeEventListener("message", handleMessage);
      });

    } else {
      console.log("🏠 顶层窗口，iframe 内容脚本跳过直接选择处理");
    }
  },
});