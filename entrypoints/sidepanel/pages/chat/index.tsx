import type { UploadProps } from "antd";
import {
  Button,
  ConfigProvider,
  Drawer,
  Dropdown,
  Empty,
  Flex,
  Mentions,
  message,
  Select,
  Space,
  Spin,
  Tag,
  theme,
  Tooltip,
  Upload,
} from "antd";
import React, { useCallback, useEffect, useRef, useState } from "react";
import AgentOutput from "@/components/AgentOutput";
import IconFont from "@/components/IconFont";
import Markdown from "@/components/Markdown";
import { createChatIcon } from "@/config/menu/chat";
import { knowdgeSVGIcon } from "@/config/menu/knowdge";
import { AIType } from "@/config/options/agent";
import { SHADOW_SIDE_PANEL } from "@/entrypoints/content/sidepanel";
import HistoryDrawer from "@/entrypoints/sidepanel/pages/chat/components/HistoryDrawer";
import Knowledge from "@/entrypoints/sidepanel/pages/chat/components/knowledge";
import WebPageFilechild from "@/entrypoints/sidepanel/pages/chat/components/webPageFilechild/index.tsx";
import useClipboard from "@/hooks/useClipboard.ts";
import { useFetchRequest } from "@/hooks/useFetchRequest.ts";
import useSSEChat, { ChatMessage, GenerationProgress } from "@/hooks/useSSEChat.ts";
import { getToken, getUserInfo, UserInfo, getTenantId } from "@/utils/auth.ts";
import SelectKnowledgeData from "@/components/SelectKnowledge";
import { debounce } from "@/utils/debounce.ts";
import { getDefaultPrompts } from "@/config/agent";
import { langList } from "@/config/options/ai";
import {
  CloseCircleFilled,
  CopyOutlined,
  ManOutlined,
  CloseCircleOutlined,
  DatabaseOutlined,
  FolderAddOutlined,
  FolderOutlined,
  HistoryOutlined,
  LayoutOutlined,
  LeftOutlined,
  PauseOutlined,
  ReadOutlined,
  RedoOutlined,
  RightOutlined,
} from "@ant-design/icons";
import { ShortcutKeyEnum } from "@/config/enums/ShortcutKeyEnum";
import { useGetState } from "ahooks";
import classNames from "classnames";
// import SelectedPrompt from "./components/SelectedPrompt";
import "./index.less";
import TopTitle from "../../components/TopTitle";
import { cacheGet } from "@/utils/browserStorage";
import { sanitizeFilename } from "@/utils/image";
import { usePermissions } from "../../components/PermissionProvider";

const { useToken } = theme;
const Chat: React.FC<{ knowledgeId }> = ({ knowledgeId }) => {
  const { permissions, setPermissions, userInfo, point } = usePermissions();
  const { token: csstoken } = useToken();
  const searchParamsInit = {
    pageNum: 1,
    pageSize: 300000,
    entity: {
      libName: "",
    },
  };

  // 当前登录人
  // const [userInfo, setUserInfo] = useState<UserInfo>({});
  const [aiList, setAiList] = useState<Array<AIType>>([]);
  const [currentAi, setCurrentAi] = useState<string>();
  const [agentId, setAgentId] = useState<string>();
  const [currentConversation, setCurrentConversation] = useState<string>("");
  const [query, setQuery, getQuery] = useGetState<string>("");
  const [, setMessageId, getMessageId] = useGetState<string>("");
  const [currentRating, setCurrentRating] = useState<string>("");
  const chatListRef = useRef<HTMLDivElement>(null);
  // 是否显示左箭头
  const [isLeftShow, setIsLeftShow] = useState(false);
  // 是否显示右箭头
  const [isRightShow, setIsRightShow] = useState(false);
  // 查询得到的提示词列表
  // const [promptList, setPromptList] = useState<any>([{ key: "1", type: "group", label: "选择提示词", children: [] }]);
  const [promptList, setPromptList] = useState<any>([]); // 下拉框提示词数据
  const [promptData, setPromptData] = useState<any>([]); // 提示词整体数据
  const [quoteData, setQuoteData] = useState<any>(""); // 引用数据
  const [isIns, setIsIns] = useState<boolean>(true); // 是否是指令
  const [openDrawer, setOpenDrawer] = useState<boolean>(false);

  // const [prompt, setPrompt] = useState<Prompt>();
  const [loading, setLoading] = useState<boolean>(false);
  const [knowledLoading, setKnowledLoading] = useState<boolean>(false); // 知识库loading

  const [openHistoryDrawer, setOpenHistoryDrawer] = useState<boolean>(false);
  const [knowledModel, setknowledModel] = useState<boolean>(false);
  const [cardData, setCardData] = useState<any>([]);
  const [removedFiles, setRemovedFiles] = useState([]);
  const [selectKnowledgeArr, setSelectKnowledgeArr] = useState<any>([]);
  const [selectKnIdsArr, setSelectKnIdsArr] = useState<any>([]); // 选中的知识信息 知识库带过来的
  const [localFile, setLocalFile] = useState<any>([]);
  const [webPageFile, setWebPageFile] = useState<any>([]);
  const [localWebPageFile, setLocalWebPageFile] = useState<any>([]);
  const [webPageFileModal, setWebPageFileModal] = useState<any>(false);
  // const [delLocalFileId, setDelLocalFileId] = useState<any>([]);
  const [allFile, setAllFile] = useState<any>([]);
  const [token, setToken] = useState<string>("");
  const [tenantId, setTenantId] = useState<string>("");
  const [konwTooltipOpen, setKonwTooltipOpen] = useState(false);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [searchParams, setSearchParams] = useState(searchParamsInit);
  // const [tempFildId, setTempFildId] = useState<string | Number[]>([]);
  const [oneWebPageArr, setOneWebPageArr] = useState<any>([]);
  const [promptFlag, setPromptFlag] = useState<boolean>(false);
  const [allFileList, setAllFileList] = useState<number>(0);
  const [dropdownVisible, setDropdownVisible] = useState(false); // 控制Dropdown显示
  const popupContainerRef = useRef(null);
  const [defaultPrompts, setDefaultPrompts] = useState([]);
  const [tags, setTags] = useState([]); // 选中的提示词
  const [sendMessageKey, setSendMessageKey] = useState("");

  const [firstMessageId, setFirstMessageId] = useState<string>(""); // 每一页的第一条消息messageId
  const [isLastPage, setIsLastPage] = useState<boolean>(true); // 标记是否最后一页
  const [isLoadingHistory, setIsLoadingHistory] = useState<boolean>(false); // 是否正在加载历史记录
  const [isDropDownLoad, setIsDropDownLoad] = useState<boolean>(false); // 标记是否可以下拉加载
  const isPageStutasRef = useRef(firstMessageId); // 用于标记是否有分页，如果有第一条消息的messageId，则说明有分页，否则没有关闭下拉加载

  const lastPrefixRef = useRef<string | null>(null); // 保存上一次触发的 prefix是/还是#
  const [triggerPrefix, setTriggerPrefix] = useState<string | null>(null);
  const [isNumberPerson, setIsNumberPerson] = useState<boolean>(false);
  const [numberPersonUrlOption, setNumberPersonUrlOption] = useState<any[]>([]);
  const [numberPersonUrl, setNumberPersonUrl] = useState<any>("");

  getToken().then((res) => {
    setToken(res);
  });
  getTenantId().then((res) => {
    setTenantId(res);
  });
  const swiperRef = useRef(null);
  /** 处理普通Web请求的hook */
  const fetchRequest = useFetchRequest();

  useEffect(() => {
    fetchRequest({
      api: "getConfig",
      params: {},
      callback: async (res) => {
        if (res.code == 200) {
          if (res?.data["ai-assistant"]["ai-humanAIGC"]) {
            let arr = [];
            for (const key in res?.data["ai-assistant"]["ai-humanAIGC"]) {
              if (Object.prototype.hasOwnProperty.call(res?.data["ai-assistant"]["ai-humanAIGC"], key)) {
                const element = res?.data["ai-assistant"]["ai-humanAIGC"][key];
                let obj = {
                  label: "形象" + (Number(key) + 1),
                  value: element,
                };
                arr.push(obj);
              }
            }
            setNumberPersonUrlOption(arr);
            setNumberPersonUrl(arr[0].value);
          }
        }
      },
    });
  }, []);

  const sseChat = useSSEChat();
  useEffect(() => {
    // getUserInfo().then((res) => {
    //   setUserInfo(res);
    // });
    // getAIList();

    // getAllListData(searchParams);
    getAgentList();
    if (knowledgeId) {
      handleKnowledgeChange(knowledgeId);
    }
    cacheGet(ShortcutKeyEnum.SENDMESSAGEKEY).then((res) => {
      setSendMessageKey(res || "Enter");
    });
  }, []);

  useEffect(() => {
    const handleSendMessageKeyChanged = (changes) => {
      if (changes[ShortcutKeyEnum.SENDMESSAGEKEY]) {
        setSendMessageKey(changes[ShortcutKeyEnum.SENDMESSAGEKEY].newValue);
      }
      if (changes["characterData"]) {
        setCurrentAi(changes["characterData"].newValue.agentId);
        historyCallback(changes["characterData"].newValue, 1);
      }
      if (changes["chatKnowLocal"]) {
        const data = changes["chatKnowLocal"].newValue.fileKnowData;
        let arr = [];
        data.forEach((item) => {
          arr.push({
            id: item.knowId,
            flag: "know",
            libName: item.name,
            libDesc: item?.extension?.replace(/^\./, ""),
            isTypeKnow: 2,
          });
        });
        setSelectKnIdsArr(arr);
      }
      // 监听页面历史聊天记录id变化 划词便签触发的
    };
    browser.storage.local.onChanged.addListener(handleSendMessageKeyChanged);
    return () => {
      browser.storage.local.onChanged.removeListener(handleSendMessageKeyChanged);
    };
  }, []);
  // 获取AI列表
  const clipboard = useClipboard();
  // 消息列表变化时自动滚动到最底部
  useEffect(() => {
    if (chatListRef.current && !isDropDownLoad) {
      // 只有在不是下拉加载历史记录的情况下才自动滚动到底部
      chatListRef.current.scrollTop = chatListRef.current.scrollHeight - chatListRef.current.clientHeight;
    }
    setPromptFlag(true);

    // if (tempFildId.length > 0 && promptFlag) {
    //   setPromptFlag(false);

    //   fetchRequest({
    //     api: "businessUpdateTmpFileConversationRela",
    //     params: {
    //       tempFileIds: tempFildId,
    //       conversationId: sseChat.currentConversation,
    //       messageId: sseChat.messageId,
    //     },
    //     callback: (res) => {
    //       if (res.code === 200) {
    //         setTempFildId([]);
    //       }
    //     },
    //   });
    // }
  }, [sseChat.displayedText, sseChat.isRendering, isDropDownLoad]);
  // 判断文件个数
  useEffect(() => {
    const shadowDom = document.getElementById(SHADOW_SIDE_PANEL).shadowRoot;
    const div = shadowDom.querySelector(".knowledge-base-info");
    if (!div) return;
    if (div.scrollWidth > div.clientWidth) {
      setIsRightShow(true);
    } else {
      setIsRightShow(false);
      setIsLeftShow(false);
    }
  }, [allFile]);
  /** 处理用户提交 */
  const handleSubmit = async (regenerate?: boolean) => {
    // 发送消息重置下拉刷新标志
    setIsDropDownLoad(false);

    // 非重新生成内容场景下，用户输入内容为空时直接返回
    if (!regenerate && !query.trim()) return;
    // 提前存好本次的提问内容，重新生成的话直接从缓存中获取之前的提问内容
    let questionCon: string = regenerate ? sessionStorage.getItem("currentInput") : query;
    let question: string = questionCon;
    sessionStorage.setItem("currentInput", question);
    // 设置好目前状态下的聊天列表数据，包含之前已经结束的沟通内容，以及本次用户的提问，本次AI的回答占位
    setQuery("");
    setCurrentRating(null);
    // if (prompt) {
    //   // 替换问题为提示词内容
    //   question = prompt.content.replaceAll("${content}", question);
    // }
    // setPrompt(null);

    if (tags && tags.length > 0) {
      // 替换问题为提示词内容
      if (tags[0].content.includes("${content}")) {
        question = tags[0].content.replaceAll("${content}", question);
        if (tags[0]?.selectedLang) {
          question = question.replaceAll("${lang}", tags[0]?.selectedLang);
        }
      } else {
        question = `${tags[0].content}\n${question}`;
      }
    }

    if (quoteData) {
      question = `${question}\n${quoteData}`;
    }
    setTags([]);
    // const tempFileId = localFile.map((item) => item.response.data);
    const selectKnowledgeId = selectKnowledgeArr.map((item) => item.id);
    const selectKnIds = selectKnIdsArr.map((item) => item.id); // 选中的知识id
    const fileData = []; // 上传的文件信息
    if (localFile && localFile.length > 0) {
      localFile.forEach((item) => {
        let obj = {
          type: item.fileType ? item.fileType : "document",
          transfer_method: "local_file",
          upload_file_id: item.id,
        };
        fileData.push(obj);
      });
    }
    // 阅读此页
    if (oneWebPageArr && oneWebPageArr.length > 0) {
      oneWebPageArr.forEach((item) => {
        let obj = {
          type: "document",
          transfer_method: "local_file",
          upload_file_id: item.id,
        };
        fileData.push(obj);
      });
    }
    // 从选择的标签页中打开
    if (localWebPageFile && localWebPageFile.length > 0) {
      localWebPageFile.forEach((item) => {
        let obj = {
          type: "document",
          transfer_method: "local_file",
          upload_file_id: item.uid,
        };
        fileData.push(obj);
      });
    }
    const token = await getToken();
    // 调用常用接口使用的
    const resultArr = [
      ...selectKnowledgeArr.map((item) => ({
        dataId: item.id,
        relaType: "near",
        dataType: "lib",
      })),
      ...selectKnIdsArr.map((item) => ({
        dataId: item.id,
        relaType: "near",
        dataType: "kn",
      })),
    ];
    // 调用常用接口
    fetchRequest({
      api: "addRecent",
      params: resultArr,
      async callback(res) {
        if (res.code === 200) {
          console.log("添加常用成功");
        }
      },
    });
    if (tags && tags.length > 0 && isIns) {
      // 指令调用
      sseChat.start({
        message: questionCon,
        url: "/dify/broker/ins/stream",
        headers: {
          // Authorization: `Bearer ${currentAi}`,
          "Content-Type": "application/json",
          Token: token || "",
        },
        body: {
          insId: "1",
          bizId: tags[0].id,
          path: "/chat-messages",
          bizType: "app:ins",
          agentId: agentId,
          difyJson: {
            inputs: {
              query: question,
              personalLibs: selectKnowledgeId.join(","),
              knIds: selectKnIds.join(","),
              Token: token || "",
              tenantid: tenantId || "",
            },
            files: fileData,
            response_mode: "streaming",
            user: userInfo?.id || "anonymous",
            auto_generate_name: "true",
            query: question,
            conversation_id: currentConversation || "",
          },
        },
        query: {
          query: question,
          conversation_id: currentConversation || "",
        },
        onFinished: () => {
          setCurrentConversation(""); // 聊天回复后直接清空聊天id
        },
        instruct: "start",
      });
    } else {
      // agent调用
      sseChat.start({
        message: questionCon,
        url: "/dify/broker/agent/stream",
        headers: {
          // Authorization: `Bearer ${currentAi}`,
          "Content-Type": "application/json",
          Token: token || "",
        },
        body: {
          insId: "1",
          bizType: "app:agent",
          bizId: agentId, // 取agentId
          agentId: agentId, // 取agentId
          path: "/chat-messages",
          difyJson: {
            inputs: {
              query: question,
              personalLibs: selectKnowledgeId.join(","),
              knIds: selectKnIds.join(","),
              Token: token || "",
              tenantid: tenantId || "",
            },
            files: fileData,
            response_mode: "streaming",
            user: userInfo?.id || "anonymous",
            auto_generate_name: "true",
            query: question,
            conversation_id: currentConversation || "",
          },
        },
        query: {
          query: question,
          conversation_id: currentConversation || "",
        },
        onFinished: () => {
          setCurrentConversation(""); // 聊天回复后直接清空聊天id
        },
        instruct: "start",
      });

      // sseChat.start({
      //   message: questionCon,
      //   url: "/chat-messages",
      //   headers: {
      //     Authorization: `Bearer ${currentAi}`,
      //     "Content-Type": "application/json",
      //     Token: token || "",
      //   },
      //   body: {
      //     inputs: {
      //       query: question,
      //       personalLibs: selectKnowledgeId.join(","),
      //     },
      //     files: fileData,
      //     response_mode: "streaming",
      //     user: userInfo?.id || "anonymous",
      //     auto_generate_name: "true",
      //   },
      //   query: {
      //     query: question,
      //     conversation_id: currentConversation || "",
      //   },
      //   onFinished: (result) => {
      //     setCurrentConversation(""); // 聊天回复后直接清空聊天id
      //   },
      //   instruct: "start",
      // });
    }
  };
  // 初始化开场白
  const getPrologue = (): ChatMessage => {
    let prologue: ChatMessage;
    aiList.forEach((item) => {
      if (item?.appKey == currentAi) {
        prologue = {
          id: `system-${Date.now()}`,
          role: "system",
          content: item.agentDesc,
        };
      }
    });
    return prologue;
  };

  /** 清空一切，回归初始状态 */
  const handleClear = async (type?: number, fromAgentChange?: boolean) => {
    sseChat.reset().then(() => {
      // 所有状态回归到初始
      setQuery("");
      // setPrompt(null);
      sseChat.setCurrentConversation("");
      // setPromptList([{ key: "1", type: "group", label: "选择提示词", children: [] }]);

      if (!fromAgentChange) {
        setTags([]);
        setPromptList([]);
      }
      setQuoteData("");
      setMessageId("");
      setCurrentRating(null);
      setCurrentConversation("");
      sessionStorage.removeItem("currentInput");
      if (localFile && localFile.length > 0) {
        localFile.forEach((item) => {
          setRemovedFiles((prev) => [...prev, item]);
        });
      }
      if (type != 1) {
        setOneWebPageArr([]);
        setSelectKnowledgeArr([]);
        setSelectKnIdsArr([]);
        setLocalFile([]);
        setLocalWebPageFile([]);
        setWebPageFile([]);
      }
      setPromptFlag(false);
      // setTempFildId([]);
      // 重置下拉加载相关参数
      setIsDropDownLoad(false);
      setFirstMessageId("");
    });
  };

  /** 处理提示词搜索 */
  const handlePromptSearch = (
    queryText: string,
    currentAi: string | number,
    aiList: any,
    trigger: string,
    agentId: string,
  ) => {
    setPromptList([]);
    setPromptData([]);
    if (trigger === "#") {
      fetchRequest({
        api: "listPrompts",
        params: {
          agentId: agentId || "",
        },
        callback: (res) => {
          if (res.code === 200) {
            const transformedList = res.data.map((item) => ({
              ...item,
              key: item.id,
              label: item.title,
              bizType: "prompt",
            }));
            setPromptList(transformedList);
            setPromptData(transformedList);
            setDropdownVisible(true);
            setLoading(false);
          }
        },
      });
    } else {
      let id = "";
      aiList.forEach((item: any) => {
        if (item.appKey == currentAi) {
          id = item.id;
        }
      });
      fetchRequest({
        api: "getAcctIns",
        params: {
          queryContent: "",
          insShowType: "2",
        },
        callback(res) {
          if (res.code === 200) {
            let arrShow = [];
            res.data.forEach((item) => {
              if (item.accountShowFlag !== 0) {
                arrShow.push(item);
              }
            });
            defaultPrompts.forEach((item: any) => {
              if (item.id == "100180") {
                item.children = langList.map((lang) => ({
                  key: lang.key,
                  label: lang.label,
                }));
              }
            });
            setPromptList(
              [...defaultPrompts, ...arrShow].map((item) => ({
                key: item.id,
                children: item.children || [],
                label: (
                  <Flex align="center" justify="space-between">
                    <span>{item.name || item.title}</span>
                    {/* 指令请求一次消耗积分显示 */}
                    {!!item.pointsCost && item.pointsCost > 0 && (
                      <Flex align="center">
                        <span style={{ color: csstoken.blue }}>{item.pointsCost}&nbsp;</span>
                        <IconFont type="PointsCost" className="icon" />
                      </Flex>
                    )}
                  </Flex>
                ),
              })),
            );
            setPromptData([...defaultPrompts, ...arrShow]);
            setDropdownVisible(true);
            setLoading(false);
          } else {
            message.open({
              type: "error",
              content: res.msg,
            });
          }
        },
      });
    }
  };

  /** 防抖搜索提示词 */
  const debouncePromptSearch = useCallback(debounce(handlePromptSearch, 500), []);
  // AI助手切换
  const handleRoleChange = (value) => {
    browser.storage.local.set({ current_ai_appKey: value }).then(async () => {
      setCurrentAi(value);
      await handleClear(1);
    });
  };
  useEffect(() => {
    findAgentId(currentAi);
    handleClear(1, true);
  }, [currentAi]);
  // AI助手选中后根据appkey找到id
  const findAgentId = (value) => {
    aiList.find((item) => {
      if (item.appKey === value) {
        setAgentId(item.id);
      }
    });
  };
  /** 处理停止 */
  const handleStop = () => {
    sseChat.stop(agentId, currentAi, userInfo?.id || "anonymous");
  };

  // 弹出历史记录
  const handleHistory = () => {
    setOpenHistoryDrawer(true);
  };

  // 关闭历史记录
  const historyDrawerClose = () => {
    setOpenHistoryDrawer(false);
  };

  /**
   * 回显聊天记录信息
   * @param current
   */
  const historyCallback = (current, type?) => {
    historyDrawerClose();
    sseChat.reset();
    loadConversationHistory(current, "", true, false);
  };
  // 监听firstMessageId变化 使用 useRef 来保存最新值，保证下拉加载监听中始终能拿到firstMessageId最新值
  // 用于标记是否有分页，如果有第一条消息的messageId，则说明有分页，否则没有关闭下拉加载
  useEffect(() => {
    isPageStutasRef.current = firstMessageId;
  }, [firstMessageId]);

  // 监听下拉加载动作
  useEffect(() => {
    const handleScroll = debounce(() => {
      if (!chatListRef.current) return;

      const { scrollTop } = chatListRef.current;

      // 判断是否接近顶部
      if (scrollTop <= 50) {
        // 不是正在加载历史记录时且不是最后一页时且有firstMessageId时，则开始加载历史记录
        if (!isLoadingHistory && !isLastPage && isPageStutasRef.current) {
          loadConversationHistory(
            { conversationId: currentConversation || sseChat.currentConversation },
            firstMessageId,
            false,
            true,
          );
        }
      }
    }, 200);

    chatListRef.current.addEventListener("scroll", handleScroll);

    return () => {
      chatListRef.current?.removeEventListener("scroll", handleScroll);
    };
  }, [currentConversation, firstMessageId, isLoadingHistory, isLastPage]);

  /**
   * 加载聊天历史记录
   * @param conversationId 会话ID
   * @param page 页码
   * @param isFirstLoad 是否首次加载
   */
  const loadConversationHistory = (
    current: any,
    firstMessageId: string,
    isFirstLoad: boolean = false,
    isDropDownLoad: boolean = false,
  ) => {
    setIsLoadingHistory(true);

    // 保存请求前的那一条消息的位置
    const chatContainer = chatListRef.current;
    let firstMessageIdBeforeLoad = "";

    if (isDropDownLoad && chatContainer && sseChat.chatList.length > 0) {
      firstMessageIdBeforeLoad = sseChat.chatList[0][0].messageId;
    }

    fetchRequest({
      api: "getConversation",
      params: {
        conversationId: current.conversationId,
        messageId: firstMessageId,
        size: 5,
      },
      callback: (res) => {
        setIsLoadingHistory(false);
        const content = res.data?.content || [];
        const newMessages = content.map((item) => [
          // 组装用户消息
          {
            messageId: item.messageId,
            id: `user-${Date.now()}`,
            role: "user",
            content: item.query,
          },
          // 组装本次回复，用来占位，此时内容为空
          {
            messageId: item.messageId,
            id: `assistant-${Date.now()}`,
            role: "assistant",
            content: item.answer,
          },
        ]);
        if (isFirstLoad) {
          // 首次加载：清空旧数据 + 设置当前会话 ID
          sseChat.setChatList(newMessages);
        } else {
          // 下拉加载更多：追加到现有列表顶部
          sseChat.setChatList([...newMessages, ...sseChat.chatList]);
        }
        // 存入每一页的第一条消息messageId，给后端，用于获取此条消息之前的消息，结合size使用
        const firstMsg = newMessages[0];
        setFirstMessageId(firstMsg[0].messageId);

        setCurrentConversation(current.conversationId);
        // 标记是否最后一页
        setIsLastPage(res.data.last);
        // 标记是否下拉加载
        setIsDropDownLoad(isDropDownLoad);
        if (content.length > 0 && !isDropDownLoad) {
          sessionStorage.setItem("currentInput", content[content.length - 1].query);
        }
        sseChat.setDisplayedText(newMessages.length > 0 ? newMessages[newMessages.length - 1][1].content : "");

        // 滚动回原来的第一条消息，并固定在顶部
        if (isDropDownLoad && firstMessageIdBeforeLoad && chatContainer) {
          const tryScroll = () => {
            const elements = chatContainer.querySelectorAll("[data-message-id]");
            for (let el of elements) {
              if (el.getAttribute("data-message-id") === firstMessageIdBeforeLoad) {
                el.scrollIntoView({ behavior: "instant", block: "start" });
                chatContainer.scrollTop -= 100;
                return;
              }
            }
          };

          setTimeout(tryScroll, 0);
        }
      },
    });
  };
  const uploadFile: UploadProps = {
    name: "file",
    multiple: true,
    headers: {
      [import.meta.env["VITE_API_HEADER_KEY"]]: token,
    },
    showUploadList: false,
    accept: ".docx,.pptx,.xls,.xlsx,.csv,.txt,.pdf,.png,.gif,.jpg,.jpeg,.JPG,.PNG,.JPEG,.GIF",
    // accept: ".docx,.pptx,.xls,.xlsx,.csv,.txt,.pdf",
    beforeUpload(file) {
      if (localFile.length > 5) {
        message.error("最多上传5个附件");
        return Promise.reject(); // 返回拒绝的 Promise 阻止上传
      }
      const isLt2M = file.size / 1024 / 1024 < 15; // 限制文件大小为15MB
      if (!isLt2M) {
        message.error("不允许超过15MB!");
        return Promise.reject(); // 返回拒绝的 Promise 阻止上传
      }
      let arr = file.name.split(".");
      let fileName = arr[arr.length - 1] || "";
      let fileFormat = [
        "docx",
        "pptx",
        "xls",
        "xlsx",
        "csv",
        "txt",
        "pdf",
        "gif",
        "jpeg",
        "png",
        "jpg",
        "JPG",
        "PNG",
        "JPEG",
        "GIF",
      ];
      // let fileFormat = ["docx", "pptx", "xls", "xlsx", "csv", "txt", "pdf"];
      if (!fileFormat.includes(fileName)) {
        message.error("文件格式不正确!");
        return Promise.reject(); // 返回拒绝的 Promise 阻止上传
      }
    },
    // onChange(info) {
    //   let { fileList } = info;
    //   fileList = fileList.filter((f) => f.status);
    //   setAllFileList(fileList.length);
    //   const updatedFileList = fileList.filter((f) => !removedFiles.some((removed) => removed.uid === f.uid));
    //   const processedFiles = updatedFileList.map((item) => {
    //     // 在上传中标记 loading
    //     if (item.status === "uploading") {
    //       return {
    //         ...item,
    //         loading: true,
    //         flag: "file",
    //         libName: item.name,
    //         libDesc: getExtensionFromMimeType(item.type),
    //       };
    //     }
    //     if (item.status === "done") {
    //       setPromptFlag(false);
    //       setTempFildId([item.response.data, ...tempFildId]);
    //       return {
    //         ...item,
    //         loading: false,
    //         flag: "file",
    //         libName: item.name,
    //         libDesc: getExtensionFromMimeType(item.type),
    //       };
    //     }
    //     if (item.status === "error") {
    //       return { ...item, loading: false }; // 上传失败时去掉 loading
    //     }
    //     return item;
    //   });
    //   setLocalFile([...processedFiles]);
    // },
  };

  // 文件转base64
  function fileToBase64(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();

      // 成功读取文件时的回调
      reader.onload = () => {
        resolve(reader.result); // Base64 编码的字符串
      };

      // 读取文件失败时的回调
      reader.onerror = (error) => {
        reject(error);
      };

      // 读取文件并转为 Base64
      reader.readAsDataURL(file);
    });
  }

  let uploadQueue: any[] = []; // 存储待上传的文件
  let isUploading = false; // 标记是否正在上传

  const processQueue = async () => {
    if (uploadQueue.length === 0) {
      isUploading = false; // 队列为空时，停止上传状态
      return;
    }

    isUploading = true;
    const { file, type } = uploadQueue.shift(); // 取出队列中的第一个任务
    await uploadFileNew(file, type); // 执行上传

    setTimeout(processQueue, 1000); // 1 秒后处理下一个任务
  };

  const handleCustomRequest = async (options: any) => {
    const { file } = options;
    uploadQueue.push({ file, type: 2 }); // 将文件任务加入队列

    if (!isUploading) {
      processQueue(); // 如果当前没有正在上传的任务，启动队列
    }
    // 默认切换成通用聊天助手
    // browser.storage.local.set({ current_ai_appKey: aiList[0].appKey });
    // setCurrentAi(aiList[0].appKey);
  };

  const getExtensionFromMimeType = (mimeType) => {
    switch (mimeType) {
      case "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
        return "docx";
      case "text/plain":
        return "txt";
      case "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
        return "xlsx";
      case "text/csv":
        return "csv";
      case "application/vnd.ms-excel":
        return "xls";
      case "application/vnd.openxmlformats-officedocument.presentationml.presentation":
        return "pptx";
      default:
        return "pdf"; // 默认扩展名
    }
  };
  const uploadFileMaxCount = () => {
    return allFileList + (5 - localFile.length);
  };

  const items = [
    {
      key: "2",
      label: (
        <Flex align="center">
          <Flex className="tips">
            <DatabaseOutlined />{" "}
          </Flex>
          知识库
          <Flex className="upload-desc">选择询问的知识库</Flex>
        </Flex>
      ),
    },
    {
      key: "3",
      label: (
        <Upload
          {...uploadFile}
          maxCount={uploadFileMaxCount()}
          customRequest={handleCustomRequest}
          className="sino-upload-file"
        >
          <Flex className="upload-txt" align="center">
            <Flex className="tips">
              <FolderOutlined />
            </Flex>
            本地文件
            <Flex className="upload-desc">上传本地文件</Flex>
          </Flex>
        </Upload>
      ),
    },
    {
      key: "1",
      label: (
        <Flex align="center">
          <Flex className="tips">
            <LayoutOutlined />
          </Flex>
          从已打开的标签页选择
        </Flex>
      ),
    },
  ];
  const fileHandler = (item) => {
    if (item.key === "1") {
      setWebPageFileModal(true);
      bookHandler();
    } else if (item.key === "2") {
      //  知识库
      setOpenDrawer(true);
      // debounceNoteSearch(searchParams, selectKnowledgeArr);
      // setknowledModel(true);
    }
  };
  const bookHandler = () => {
    chrome.runtime.sendMessage({ action: "getTabs" }, (response) => {
      if (response) {
        // 显示所有标签页的信息
        response.forEach((tab) => {
          tab.libName = tab.title.replace(/[\r\n]/g, "");
          tab.favIconUrl = tab.favIconUrl || browser.runtime.getURL("/images/ico.png");
        });
        let arrUrl = [];
        let arrInfo = [];
        for (let i = 0; i < response.length; i++) {
          for (let j = 0; j < webPageFile.length; j++) {
            if (webPageFile[j].id === response[i].id) {
              response[i].checked = webPageFile[i]?.checked || false;
            }
          }
          if (!arrUrl.includes(response[i].url)) {
            arrUrl.push(response[i].url);
            arrInfo.push(response[i]);
          }
        }
        setWebPageFile(arrInfo);
      }
    });
  };

  const oneWebPageHtmlFileHandler = () => {
    let num = 0;
    oneWebPageArr.forEach((element) => {
      if (element.libDesc == window.location.href) {
        num++;
      }
    });
    if (num < 1) {
      const file = new File([document.body.innerText], document.title + ".txt", { type: "text/plain" });
      let data = {
        libDesc: window.location.href,
        libName: document.title,
      };
      uploadFileNew(file, 1, data);
    }
    // 默认切换成通用聊天助手
    // browser.storage.local.set({ current_ai_appKey: aiList[0].appKey });
    // setCurrentAi(aiList[0].appKey);
  };
  const oneWebPageFileHandler = async (dataUrl) => {
    oneWebPageHtmlFileHandler();
    try {
      const response: any = await new Promise((resolve, reject) => {
        chrome.runtime.sendMessage({ type: "captureTab" }, (response) => {
          if (response?.error) {
            reject(response.error);
          } else {
            resolve(response);
          }
        });
      });

      const dataUrl = response.dataUrl;

      const dataURLtoFile = (dataurl: string, filename: string): File => {
        const arr = dataurl.split(",");
        const mime = arr[0].match(/:(.*?);/)?.[1] || "image/png";
        const bstr = atob(arr[1]);
        let n = bstr.length;
        const u8arr = new Uint8Array(n);

        while (n--) {
          u8arr[n] = bstr.charCodeAt(n);
        }

        return new File([u8arr], filename, { type: mime });
      };

      const file = dataURLtoFile(dataUrl, document.title + ".png");
      const data = {
        libName: document.title,
        libDesc: window.location.href,
      };

      uploadFileNew(file, 2, data);
    } catch (error) {
      // message.error("截图或上传失败");
      console.error("截图或上传失败");
    }
  };
  const getFavicon = async () => {
    let iconLink = "";
    const sendMessage = (message) => {
      return new Promise((resolve, reject) => {
        chrome.runtime.sendMessage(message, (response) => {
          if (chrome.runtime.lastError) {
            reject(chrome.runtime.lastError);
          } else {
            resolve(response);
          }
        });
      });
    };
    try {
      const response = await sendMessage({ action: "getTabs" });
      if (response) {
        // 处理标签页信息
        response.forEach((tab) => {
          tab.libName = tab.title.replace(/[\r\n]/g, "");
          if (tab.url == window.location.href) {
            iconLink = tab.favIconUrl || browser.runtime.getURL("/images/ico.png");
          }
        });
      }
    } catch (error) {
      console.error("Error:", error);
    }
    if (iconLink) {
      return iconLink;
    } else {
      return browser.runtime.getURL("/images/ico.png");
    }
  };
  // 上传文件
  let queue: Promise<void> = Promise.resolve(); // 初始化 Promise 队列

  const uploadFileNew = (file, type, data?) => {
    queue = queue.then(() => {
      return new Promise((resolve, reject) => {
        let fileData: any = {
          fileName: "",
          fileStr: "",
        };
        if (!file) {
          resolve();
          return;
        }

        // 使用 async/await 来处理异步逻辑
        (async () => {
          let sanitizeName = await sanitizeFilename(file.name);
          try {
            fileData = {
              fileName: sanitizeName,
              fileStr: await fileToBase64(file),
              loading: true,
              path: "/files/upload",
              agentId: agentId,
              user: userInfo?.id,
            };

            if (type == 1) {
              fileData.libName = data.libName;
              fileData.libDesc = data.libDesc;
              fileData.flag = "oneWebPageFile";
              fileData.faviocn = await getFavicon();
              setOneWebPageArr([...oneWebPageArr, fileData]);
            } else if (type == 2) {
              fileData.libName = sanitizeName;
              let arr = file.name.split(".");
              fileData.libDesc = arr[arr.length - 1];
              fileData.flag = "file";
              setLocalFile((prevFiles) => {
                if (prevFiles.some((item) => item.libName === fileData.libName)) {
                  message.open({
                    type: "warning",
                    content: "已经添加了，不可重复添加",
                  });
                  return prevFiles;
                }
                return [...prevFiles, fileData];
              });
            } else if (type == 3) {
              let selectIdFilterArr = webPageFile
                .filter(
                  (itemArr) =>
                    sanitizeName.substring(0, sanitizeName.lastIndexOf(".")) == sanitizeFilename(itemArr.title),
                )
                .map((itemArr) => ({
                  flag: "webPageFile",
                  libName: sanitizeFilename(itemArr.title),
                  libDesc: itemArr.url,
                  faviocn: itemArr.favIconUrl,
                  id: itemArr.id,
                  loading: true,
                }));
              setLocalWebPageFile((prev) => [...prev, ...selectIdFilterArr]);
            }
          } catch (error) {
            console.error("文件转 Base64 出错：", error);
            reject(); // 发生错误时调用 reject
            return;
          }

          fetchRequest({
            api: "uploadChatFile",
            params: fileData,
            file: true,
            callback: async (response) => {
              const res = response.data;
              if (res?.name) {
                if (type == 1) {
                  res.libName = data.libName;
                  res.libDesc = data.libDesc;
                  res.flag = "oneWebPageFile";
                  res.faviocn = await getFavicon();
                  setOneWebPageArr((prevArr) =>
                    prevArr.map((item) => (item.libName === res.libName ? { ...item, ...res, loading: false } : item)),
                  );
                } else if (type == 2) {
                  res.libName = res.name;
                  res.libDesc = res.extension;
                  res.flag = "file";
                  // res.fileType = "document";
                  const imgType = ["jpg", "png", "jpeg", "gif", "JPG", "PNG", "JPEG", "GIF"];
                  if (imgType.includes(res.extension)) {
                    res.fileType = "image";
                  } else {
                    res.fileType = "document";
                  }
                  setLocalFile((prevArr) =>
                    prevArr.map((item) => (item.libName === res.name ? { ...item, ...res, loading: false } : item)),
                  );
                } else if (type == 3) {
                  setLocalWebPageFile((prevArr) =>
                    prevArr.map((item) =>
                      item.libName == res.name.substring(0, res.name.lastIndexOf("."))
                        ? {
                            ...item,
                            loading: false,
                            uid: res.id,
                            size: res.size,
                            mime_type: res.mime_type,
                            extension: res.extension,
                          }
                        : item,
                    ),
                  );
                }
              } else {
                message.open({
                  type: "error",
                  content: "上传失败",
                });
              }
              resolve(); // 上传完成后调用 resolve
            },
          });
        })();
      });
    });
  };

  const closeKnowledModelFalg = () => {
    setknowledModel(false);
  };
  const closeKnowledModel = (modalFalg, selectId = []) => {
    let selectIdArr = selectId;
    setknowledModel(modalFalg);

    let selectIdFilterArr = cardData
      .filter((itemArr) => selectIdArr.includes(itemArr.id)) // 过滤出符合条件的元素
      .map((itemArr) => ({
        id: itemArr.id,
        flag: "knowledge",
        libName: itemArr.libName,
        libDesc: itemArr.libDesc,
      }));
    setSelectKnowledgeArr(selectIdFilterArr);
  };
  // 这个是提交的
  const closeWebPageModel = (modalFalg, selectId = []) => {
    let selectIdArr = selectId;
    if (oneWebPageArr && setOneWebPageArr.length > 0) {
      if (selectIdArr && selectIdArr.length > 4) {
        message.open({
          type: "error",
          content: "最多可以选择4个",
        });
        return;
      }
    } else {
      if (selectIdArr && selectIdArr.length > 5) {
        message.open({
          type: "error",
          content: "最多可以选择5个",
        });
        return;
      }
    }
    setWebPageFileModal(false);
    // 默认切换成通用聊天助手
    // browser.storage.local.set({ current_ai_appKey: aiList[0].appKey });
    // setCurrentAi(aiList[0].appKey);
    chrome.runtime.sendMessage({ action: "getSelectedTabsContent", tabIds: selectIdArr }, (response) => {
      response.forEach((tabContent) => {
        const file = new File([tabContent.innerText?.innerText], tabContent.innerText?.title + ".txt", {
          type: "text/plain",
        });
        uploadFileNew(file, 3, selectIdArr);
      });
    });
  };
  const closeWebPageFalg = () => {
    setWebPageFileModal(false);
  };
  const handleCheckboxWebPageChange = (id, newChecked) => {
    const updatedItems = webPageFile.map((item) => (item.id === id ? { ...item, checked: newChecked } : item));
    setWebPageFile(updatedItems);
  };
  // const keywordSearch = (value) => {
  //   const updateValue = { ...searchParams, entity: { libName: value } };
  //   setSearchParams(updateValue);
  //   setKnowledLoading(true);
  //   debounceNoteSearch(updateValue, selectKnowledgeArr);
  // };
  // const getAllListDataSearch = (updateSearchParams, latestSelectKnowledgeArr) => {
  //   getAllListData(updateSearchParams, latestSelectKnowledgeArr);
  // };
  // const debounceNoteSearch = useCallback(debounce(getAllListDataSearch, 500), []);

  // const getAllListData = (searchParamsInit, latestSelectKnowledgeArr?) => {
  //   setKnowledLoading(true);
  //   fetchRequest({
  //     api: "getKnowledgeData",
  //     params: searchParamsInit,
  //     callback: (res) => {
  //       if (res.code === 200) {
  //         let data = res.data.records;
  //         // for (let i = 0; i < data.length; i++) {
  //         //   // 检查当前项是否在已选择的知识库数组中
  //         //   if (latestSelectKnowledgeArr) {
  //         //     const matched = latestSelectKnowledgeArr.some((item) => item.id === data[i].id);
  //         //     data[i].checked = matched;
  //         //   } else {
  //         //     data[i].checked = false;
  //         //   }
  //         // }
  //         // setCardData(data);
  //       }
  //       setKnowledLoading(false);
  //     },
  //   });
  // };
  // 拿到选择的知识库，知识
  const handleKnowledgeChange = (data) => {
    // 1 是知识库 2是知识
    let selectKnowledgeIdArr = data
      .filter((item) => item.isTypeKnow == "1")
      .map((item) => ({
        id: item.id,
        flag: "knowledge",
        libName: item.libName,
        libDesc: item.libDesc,
        isTypeKnow: 1,
      }));
    setSelectKnowledgeArr(selectKnowledgeIdArr);

    // 知识
    const filterArr = data
      .filter((item) => item.isTypeKnow === "2")
      .map((item) => ({
        id: item.id,
        flag: "know",
        libName: item.fileName,
        libDesc: item.fileType,
        isTypeKnow: 2,
      }));

    setSelectKnIdsArr(filterArr);
  };
  const handleDeleteKnowledge = (itemObj, index) => {
    if (itemObj.flag === "know") {
      setSelectKnIdsArr(selectKnIdsArr.filter((item, i) => item.id !== itemObj.id));
    }
    if (itemObj.flag === "knowledge") {
      setSelectKnowledgeArr(selectKnowledgeArr.filter((item, i) => item.id !== itemObj.id));
    } else if (itemObj.flag === "file") {
      setRemovedFiles((prev) => [...prev, itemObj]);
      setLocalFile((prevFiles) => prevFiles.filter((f) => f.id !== itemObj.id));
    } else if (itemObj.flag === "webPageFile") {
      setLocalWebPageFile(localWebPageFile.filter((item, i) => item.id !== itemObj.id));
      for (let i = 0; i < webPageFile.length; i++) {
        if (webPageFile[i].id == itemObj.id) {
          webPageFile[i].checked = false;
        }
      }
      setWebPageFile(webPageFile);
    } else {
      const newOneWebPageArr = oneWebPageArr.filter((item, i) => item.id !== itemObj.id);
      setOneWebPageArr(newOneWebPageArr);
    }
  };
  const fileExtensionHandler = (item) => {
    if (item.libDesc === "pdf") {
      return <span className="extend-icon">{knowdgeSVGIcon.pdf}</span>;
    } else if (item.libDesc === "docx") {
      return <span className="extend-icon">{knowdgeSVGIcon.word}</span>;
    } else if (item.libDesc === "xls" || item.libDesc === "xlsx" || item.libDesc === "csv") {
      return <span className="extend-icon">{knowdgeSVGIcon.excel}</span>;
    } else if (item.libDesc === "txt") {
      return <span className="extend-icon">{knowdgeSVGIcon.txt}</span>;
    } else if (item.libDesc === "pptx") {
      return <span className="extend-icon">{knowdgeSVGIcon.ppt}</span>;
    }
  };
  const getAgentList = async () => {
    // let data: any = [
    //   {
    //     id: "6f4b7cf6-cb59-4101-8243-83b749163a97",
    //     agentName: "通用助手",
    //     appKey: "app-7jyQDaIppw6YBcjqKD9xzyKW",
    //     agentDesc:
    //       "# 欢迎使用LNFCSEEK\n**你好，我是你的LNFCSEEK助手，很高兴能够为您提供帮助和支持。**\n\n### 一些可能的问题：\n- 给我一段文本，让我提炼总结一下；\n- 提供场景给我，生成文件的大纲；\n- 给我一段需求和要求，生成一段代码。\n- 如果您有其他问题或需求，也欢迎随时向我提问。祝您使用愉快！",
    //     tenantId: "680180",
    //     createBy: "596173209083939840",
    //     createTime: "2025-08-06 11:09:15",
    //     updateBy: "596173209083939840",
    //     updateTime: "2025-08-06 11:09:15",
    //     delFlag: "0",
    //     deptId: null,
    //     model: "advanced-chat",
    //     agentScore: "0",
    //     agentTypeId: null,
    //     isPutAway: 1,
    //     tags: "",
    //     sort: "1",
    //   },
    // ];
    // setAiList(data);
    // const currentAIKey = await browser.storage.local.get("current_ai_appKey");
    // let currentKey = currentAIKey?.current_ai_appKey;
    // // 防止切换更换权限时用户保存的默认值不在下拉选项中
    // if (!data.map((item) => item.appKey).includes(currentKey)) {
    //   currentKey = data[0].appKey;
    // }
    // browser.storage.local.set({ current_ai_appKey: currentKey });
    // setCurrentAi(currentKey);
    fetchRequest({
      api: "listAgents",
      params: {},
      callback: async (res) => {
        if (res.code === 200) {
          let data = [...res.data];
          setAiList(data);
          const currentAIKey = await browser.storage.local.get("current_ai_appKey");
          let currentKey = currentAIKey?.current_ai_appKey;
          // 防止切换更换权限时用户保存的默认值不在下拉选项中
          if (!data.map((item) => item.appKey).includes(currentKey)) {
            currentKey = data[0].appKey;
          }
          browser.storage.local.set({ current_ai_appKey: currentKey });
          setCurrentAi(currentKey);
        }
      },
    });
  };
  // const handleCheckboxChange = (id, newChecked) => {
  //   const updatedItems = cardData.map((item) => (item.id === id ? { ...item, checked: newChecked } : item));
  //   setCardData(updatedItems);
  // };
  useEffect(() => {
    setAllFile([...localFile, ...selectKnowledgeArr, ...selectKnIdsArr, ...localWebPageFile, ...oneWebPageArr]);
    if (
      localFile.length > 0 ||
      selectKnowledgeArr.length > 0 ||
      localWebPageFile.length > 0 ||
      oneWebPageArr.length > 0 ||
      selectKnIdsArr.length > 0
    ) {
      setQuoteData("");
    }
  }, [localFile, selectKnowledgeArr, selectKnIdsArr, localWebPageFile, oneWebPageArr]);

  useEffect(() => {
    setTimeout(() => {
      const shadowDom = document.getElementById(SHADOW_SIDE_PANEL).shadowRoot;
      const div = shadowDom.querySelector(".knowledge-base-info");
      if (!div) return;
      const scrollableDistanceX = div.scrollWidth - div.clientWidth;
      if (scrollableDistanceX > 0) {
        // 是否有滚动条
        if (div.scrollLeft > 0) {
          // 距离左侧是否有距离
          setIsRightShow(true);
          setIsLeftShow(true);
        } else {
          setIsRightShow(true);
          setIsLeftShow(false);
        }
      } else {
        setIsRightShow(false);
        setIsLeftShow(false);
      }
    }, 300);
  }, [localFile]);
  // 点击左右箭头
  const translateX = (type: number) => {
    const shadowDom = document.getElementById(SHADOW_SIDE_PANEL).shadowRoot;
    const div = shadowDom.querySelector(".knowledge-base-info");
    const scrollableDistanceX = div.scrollWidth - div.clientWidth;
    if (type == 1) {
      if (div.scrollLeft + 114 > scrollableDistanceX) {
        if (scrollableDistanceX > 0) {
          setIsRightShow(false);
          setIsLeftShow(true);
        }
        div.scrollTo({
          left: scrollableDistanceX,
          behavior: "smooth", // 如果想要平滑滚动，可以加上此选项
        });
      } else {
        if (scrollableDistanceX > 0) {
          setIsRightShow(true);
          setIsLeftShow(true);
        }
        div.scrollTo({
          left: div.scrollLeft + 114,
          behavior: "smooth", // 如果想要平滑滚动，可以加上此选项
        });
      }
    } else {
      if (div.scrollLeft - 114 < 0) {
        if (scrollableDistanceX > 0) {
          setIsLeftShow(false);
          setIsRightShow(true);
        }
        div.scrollTo({
          left: 0,
          behavior: "smooth", // 如果想要平滑滚动，可以加上此选项
        });
      } else {
        if (scrollableDistanceX > 0) {
          setIsRightShow(true);
          setIsLeftShow(true);
        }
        div.scrollTo({
          left: div.scrollLeft - 114,
          behavior: "smooth", // 如果想要平滑滚动，可以加上此选项
        });
      }
    }
  };
  const handleButtonClick = () => {
    if (dropdownVisible) {
      setDropdownVisible(false);
    } else {
      setLoading(true);
      debouncePromptSearch("", currentAi, aiList, "", agentId);
    }
  };
  // 选中提示词
  const handlePromptClick = (e) => {
    // 立即关闭下拉菜单，无论是一级还是二级菜单项
    setDropdownVisible(false);

    // 处理二级菜单项（翻译语言选择）
    if (e.keyPath && e.keyPath.length > 1) {
      // 这是二级菜单项，e.keyPath[1] 是父菜单项的key，e.key 是子菜单项的key
      const parentKey = e.keyPath[1];
      const childKey = e.key;

      // 查找父级提示词项
      const selectedItem = promptData.find((item) => item.id === parentKey);
      if (selectedItem && parentKey === "100180") {
        // 翻译功能，根据选择的语言设置内容
        const selectedLang = langList.find((lang) => lang.key === childKey);
        let obj = {
          id: agentId, // 翻译使用agent
          icon: selectedItem.iconRelaSourceId,
          title: `${selectedItem.name || selectedItem.title} - ${selectedLang?.label || ""}`,
          content: selectedItem.tmplContent || selectedItem.content,
          selectedLang: selectedLang?.label, // 保存选择的语言key
        };
        setIsIns(false);
        setCurrentAi(selectedItem.appKey);
        setTags([obj]);
        return;
      }
    }

    // 处理一级菜单项
    const selectedItem = promptData.find((item) => item.id === e.key);
    if (!selectedItem) return;

    // 如果选中的是提示词
    const selectedPromptItem = promptList.find((item) => item.id === e.key);
    if (selectedPromptItem?.bizType === "prompt") {
      const promptContent = selectedItem.content;
      let newQuery = query;

      // 只有当是 # 触发时才清理开头的 #
      if (triggerPrefix === "#") {
        newQuery = query.replace(/^#/, "").trim();
      }

      newQuery = `${newQuery}${promptContent}`;
      setQuery(newQuery);
      setTriggerPrefix(null); // 清空触发标识
      return;
    }

    let isTrue = true;
    setIsIns(true);

    defaultPrompts.forEach((item) => {
      // 默认指令信息。如果是的话，需要走agent而不是走指令
      if (item.id == e.key) {
        setIsIns(false);
        isTrue = false;
      }
    });
    let obj = {
      id: isTrue ? selectedItem.id : agentId,
      icon: selectedItem.iconRelaSourceId,
      title: selectedItem.name || selectedItem.title || selectedItem.label,
      content: selectedItem.tmplContent || selectedItem.content,
    };
    setCurrentAi(selectedItem.appKey);
    setTags([obj]);
    // 只有当是 / 触发时才清理开头的 /
    if (triggerPrefix === "/") {
      let newQuery = query.replace(/^\//, "").trim();
      setQuery(newQuery);
      setTriggerPrefix(null); // 清空触发标识
    }
  };
  // 关闭tag
  const tagClose = () => {
    setTags([]);
  };
  // 删除引用
  const quoteClose = () => {
    setQuoteData("");
  };
  // 换行
  const handleNewline = () => {
    setQuery((prevValue) => prevValue + "\n"); // 在当前文本后添加换行符
  };
  const handleKeyDown = async (e) => {
    e.stopPropagation();
    let res = await cacheGet(ShortcutKeyEnum.SENDMESSAGEKEY);
    if (e.key === "Enter") {
      if (e.ctrlKey) {
        if (!query) return;
        res === "ctrlEnter" ? handleSubmit() : handleNewline();
      } else {
        if (res === "Enter" || !res) {
          query ? handleSubmit() : setQuery("");
        }
      }
    }
  };
  // 关闭
  const onCloseDrawer = () => {
    setOpenDrawer(false);
  };
  const onGetKnowledge = (data) => {
    handleKnowledgeChange(data);
    setOpenDrawer(false);
  };

  // 子组件清空选择了
  const closeFun = () => {
    handleKnowledgeChange([]);
  };

  const handleChange = (value: { value: string; label: React.ReactNode }) => {
    setNumberPersonUrl(value);
  };
  return (
    <>
      <TopTitle
        title="聊天"
        // rightSlot={
        //   <Flex align="center" style={{ marginRight: token.margin }}>
        //     {isNumberPerson ? (
        //       <Flex align="center">
        //         <Select
        //           value={numberPersonUrl}
        //           style={{ width: 100 }}
        //           onChange={handleChange}
        //           options={numberPersonUrlOption}
        //           getPopupContainer={(triggerNode) => triggerNode.parentNode}
        //         />
        //         <Tag
        //           className="title-count"
        //           onClick={() => setIsNumberPerson(!isNumberPerson)}
        //           style={{ marginLeft: "0px", cursor: "pointer" }}
        //         >
        //           AI聊天
        //         </Tag>
        //       </Flex>
        //     ) : (
        //       <Tag
        //         className="title-count"
        //         onClick={() => setIsNumberPerson(!isNumberPerson)}
        //         style={{ marginLeft: "0px", cursor: "pointer" }}
        //       >
        //         数字人
        //       </Tag>
        //     )}
        //   </Flex>
        // }
      ></TopTitle>
      {isNumberPerson ? (
        <Flex className="chat-container" vertical>
          <iframe src={numberPersonUrl} allow="camera; microphone" style={{ height: "100%" }}></iframe>
        </Flex>
      ) : (
        <Flex className="chat-container" vertical>
          {/* 会话列表 */}
          <Flex className="chat-list" ref={chatListRef} vertical>
            <Flex className="chat-item system">
              <AgentOutput {...getPrologue()} finished={true} />
            </Flex>
            {isLoadingHistory && (
              <Flex className="chat-item loading">
                <Spin size="small" />
                <span style={{ marginLeft: 8 }}>加载中...</span>
              </Flex>
            )}
            {sseChat.chatList.map((item, index) => {
              const userMessageId = item[0].messageId;
              // 实际的会话消息
              return (
                <Flex key={index} vertical data-message-id={userMessageId}>
                  {/* 用户提问 */}
                  <Flex className="chat-item chat-question" key={item[0].id}>
                    <Markdown content={item[0].content} finished={true} />
                  </Flex>
                  {/* AI回答 */}
                  {/* 过去已经完成的回答 */}
                  {(item[1].id !== sseChat.currentResponseId || sseChat.isRendering) && (
                    <div className="answer-block">
                      <img
                        src={browser.runtime.getURL("/images/logo.png")}
                        alt=""
                        style={{ width: 16, height: 16, position: "absolute", left: 0, top: 2 }}
                      />
                      <div
                        style={{
                          width: "calc(100% - 20px)",
                          marginLeft: "20px",
                        }}
                      >
                        <Flex className="chat-item chat-answer">
                          <AgentOutput {...item[1]} finished={true} />
                        </Flex>
                        <div className="chat-toolbar">
                          <Tooltip
                            key={index}
                            getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                            placement="top"
                            title={item[1].id + index === clipboard.copied ? "已复制" : "复制"}
                          >
                            <Flex
                              className="action-item"
                              onClick={() => {
                                copyText(item[1].content).then(() => clipboard.setCopied(item[1].id + index));
                              }}
                            >
                              {item[1].id + index === clipboard.copied ? (
                                // <img
                                //   className="icon copy"
                                //   src={browser.runtime.getURL("/images/public/copied.svg")}
                                //   alt=""
                                // />
                                <Button
                                  icon={<CopyOutlined className="icon copy icon-oper" />}
                                  type="text"
                                  size="small"
                                  className="btn-hover"
                                ></Button>
                              ) : (
                                // <img
                                //   className="icon copy"
                                //   src={browser.runtime.getURL("/images/public/uncopied.svg")}
                                //   alt=""
                                // />
                                <Button
                                  icon={<CopyOutlined className="icon copy icon-oper" />}
                                  type="text"
                                  size="small"
                                  className="btn-hover"
                                ></Button>
                              )}
                            </Flex>
                          </Tooltip>
                          {index === sseChat.chatList.length - 1 && (
                            <Tooltip
                              placement="top"
                              title="重新生成"
                              getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                            >
                              <Flex className="action-item" onClick={() => handleSubmit(true)} align="center">
                                <Flex className="icon regenerate">
                                  {/* {resetChatSVGIcon} */}
                                  <Button
                                    icon={<RedoOutlined className="icon copy icon-oper" />}
                                    type="text"
                                    size="small"
                                  ></Button>
                                </Flex>
                              </Flex>
                            </Tooltip>
                          )}
                          <Tooltip
                            key={item[1].id}
                            getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                            placement="top"
                            title="引用"
                          >
                            <Flex
                              className="action-item"
                              onClick={() => {
                                setLocalFile([]);
                                setSelectKnowledgeArr([]);
                                setSelectKnIdsArr([]);
                                setLocalWebPageFile([]);
                                setOneWebPageArr([]);
                                setQuoteData(item[1].content);
                              }}
                            >
                              <Button
                                icon={<IconFont type="Quote" />}
                                type="text"
                                size="small"
                                className="btn-hover"
                              ></Button>
                            </Flex>
                          </Tooltip>
                        </div>
                      </div>
                    </div>
                  )}
                  {/* 正在输出中的回答 */}
                  {item[1].id === sseChat.currentResponseId && sseChat.isRendering == false && (
                    <Flex className="answer-block" gap={csstoken.marginXS}>
                      <img
                        src={browser.runtime.getURL("/images/logo.png")}
                        alt=""
                        style={{ width: 16, height: 16, marginTop: "2px" }}
                      />
                      <Flex className="chat-item chat-answer responding">
                        <AgentOutput {...item[1]} content={sseChat.displayedText} finished={false} />
                      </Flex>
                    </Flex>
                  )}
                </Flex>
              );
            })}
          </Flex>
          {/* 输入区 */}
          <Flex
            vertical
            className={
              sseChat.progress == GenerationProgress.RESPONDING
                ? "chat-input-panel chat-input-panel-responding"
                : "chat-input-panel"
            }
          >
            <Flex className="stop" justify="center">
              {sseChat.progress != 0 && sseChat.isRendering == false && (
                // <div className="stop-btn" onClick={handleStop}>
                //   <Tooltip
                //     placement="top"
                //     title="停止生成"
                //     getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                //   >
                //     {stopChatSVGIcon}
                //   </Tooltip>
                // </div>
                <Button className="stop-btn" onClick={handleStop} size="small" shape="round">
                  {/* {stopChatSVGIcon} */}
                  <PauseOutlined />
                  停止生成
                </Button>
              )}
            </Flex>
            <Flex className="top-toolbar" justify="space-between" align="center">
              <Flex className="top-toolbar-left" align="center">
                <Flex align="center">
                  {/* {prompt ? <SelectedPrompt prompt={prompt} onClear={() => setPrompt(null)} /> : <span></span>} */}
                  {/* Agent选择框 */}
                  {permissions.includes("ai:assistant:chat:model") && (
                    <Flex className="chat-container-assistant">
                      <img src={browser.runtime.getURL("/images/logo.png")} alt="" />
                      <Select
                        // style={{ width: 96 }}
                        getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                        onChange={handleRoleChange}
                        value={currentAi}
                        options={aiList.map((item: AIType, index: number) => {
                          return {
                            value: `${item.appKey}`,
                            key: `${index}`,
                            label: (
                              <Tooltip
                                title={`${item.agentName}`}
                                placement="rightBottom"
                                color="#999"
                                getPopupContainer={(triggerNode) => {
                                  const shadowDom = document.getElementById("shadow-side-panel").shadowRoot;
                                  return shadowDom.getElementById("side-panel-container-web");
                                }}
                              >
                                <Flex justify="space-between" align="center">
                                  <span className="agent-name">{`${item.agentName}`}&nbsp;&nbsp;</span>
                                  {/* agent请求一次消耗积分显示 */}
                                  {!!item.agentScore && item.agentScore !== "0" && (
                                    <Flex align="center">
                                      <span style={{ color: csstoken.blue }}>{item.agentScore}&nbsp;</span>
                                      <IconFont type="PointsCost" className="icon" />
                                    </Flex>
                                  )}
                                </Flex>
                              </Tooltip>
                            ),
                          };
                        })}
                        disabled={sseChat.progress == GenerationProgress.RESPONDING}
                      />
                    </Flex>
                  )}
                  {permissions.includes("ai:assistant:chat:read") && (
                    <Flex className="upload-file">
                      <Tooltip
                        placement="top"
                        title="阅读此页"
                        getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                      >
                        <Space style={{ cursor: "pointer" }} onClick={oneWebPageFileHandler}>
                          <Button
                            shape="circle"
                            disabled={sseChat.progress == GenerationProgress.RESPONDING}
                            icon={<ReadOutlined className="icon" />}
                            className="btn-icon"
                          />
                        </Space>
                      </Tooltip>
                    </Flex>
                  )}
                  {permissions.includes("ai:assistant:chat:upload") && (
                    <Flex className="upload-file">
                      <Dropdown
                        placement="top"
                        trigger={["click"]}
                        autoAdjustOverflow={true}
                        getPopupContainer={() => {
                          const shadowDom = document.getElementById("shadow-side-panel").shadowRoot;
                          return shadowDom.getElementById("sinoFolderAddOutlined");
                        }}
                        open={dropdownOpen}
                        onOpenChange={(open) => {
                          setDropdownOpen(open);
                          if (open) {
                            setKonwTooltipOpen(false);
                          }
                        }}
                        menu={{
                          items,
                          onClick: (key) => fileHandler(key),
                          selectable: true,
                        }}
                        arrow={false}
                      >
                        <Tooltip
                          placement="top"
                          open={konwTooltipOpen}
                          onOpenChange={setKonwTooltipOpen}
                          title={
                            <Flex>
                              <ul style={{ margin: 0, padding: 0, width: "100%" }}>
                                <li>支持：docx,pptx,xls,xlsx,csv,txt,pdf</li>
                                <li>限制：单个文件15MB;最多5个附件</li>
                              </ul>
                            </Flex>
                          }
                          getPopupContainer={() => {
                            const shadowDom = document.getElementById("shadow-side-panel").shadowRoot;
                            return shadowDom.getElementById("sinoFolderAddOutlined");
                          }}
                        >
                          <Space style={{ cursor: "pointer" }}>
                            <Button
                              shape="circle"
                              disabled={sseChat.progress == GenerationProgress.RESPONDING}
                              id="sinoFolderAddOutlined"
                              icon={<FolderAddOutlined className="icon" />}
                              className="btn-icon"
                            />
                          </Space>
                        </Tooltip>
                      </Dropdown>
                    </Flex>
                  )}
                </Flex>
              </Flex>

              {/* 剩余积分显示 */}
              <Flex className="top-toolbar-right" align="center">
                {permissions.includes("ai:assistant:chat:point") && (
                  <Flex>
                    <Tooltip
                      placement="top"
                      title={`剩余积分: ${point.effectivePoint || 0}`}
                      getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                    >
                      <Button shape="circle" icon={<IconFont type="Point" className="icon" />} className="btn-icon" />
                    </Tooltip>
                  </Flex>
                )}
                {permissions.includes("ai:assistant:chat:history") && (
                  <Flex>
                    <Tooltip
                      placement="top"
                      title="历史记录"
                      getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                    >
                      <Flex
                        // disabled={sseChat.progress == GenerationProgress.RESPONDING}
                        // className={classNames(
                        //   "chat-history",
                        //   [GenerationProgress.WAITING, GenerationProgress.RESPONDING].includes(sseChat.progress) &&
                        //     "not-allowed",
                        // )}
                        onClick={() => {
                          if (sseChat.progress == GenerationProgress.RESPONDING) return;
                          handleHistory();
                        }}
                      >
                        {/* {HistoryChatSVGIcon} */}
                        <Button
                          shape="circle"
                          disabled={sseChat.progress == GenerationProgress.RESPONDING}
                          icon={<HistoryOutlined className="icon" />}
                          className="btn-icon"
                        />
                      </Flex>
                    </Tooltip>
                  </Flex>
                )}
                {permissions.includes("ai:assistant:chat:newChat") && (
                  <Flex>
                    <Tooltip
                      placement="top"
                      title="新聊天"
                      getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                    >
                      {/* Button */}
                      <Flex
                        align="center"
                        // disabled={sseChat.progress == GenerationProgress.RESPONDING}
                        className={classNames(
                          "chat-clear",
                          [GenerationProgress.WAITING, GenerationProgress.RESPONDING].includes(sseChat.progress) &&
                            "not-allowed",
                        )}
                        onClick={() => {
                          if (sseChat.progress == GenerationProgress.RESPONDING) return;
                          handleClear();
                        }}
                      >
                        {/* <Button shape="circle" icon={<HistoryOutlined className="icon" />} className="btn-icon" /> */}
                        {/* {ChatNewSVGIcon} */}
                        <Button
                          shape="circle"
                          disabled={sseChat.progress == GenerationProgress.RESPONDING}
                          icon={<IconFont type="NewchatOutlined" className="icon" />}
                          className="btn-icon"
                        />
                        {/*新聊天*/}
                      </Flex>
                    </Tooltip>
                  </Flex>
                )}
              </Flex>
            </Flex>
            <Flex
              className={classNames("text-input", { "text-input-border": allFile.length > 0 || quoteData })}
              vertical
            >
              {quoteData && (
                <Flex className="text-input-content" vertical>
                  <Flex className="quote-tit">引用文本</Flex>
                  <Flex className="quote-text">{quoteData}</Flex>
                  <Button
                    icon={<CloseCircleOutlined className="icon copy icon-oper" />}
                    type="text"
                    size="small"
                    className="quote-close"
                    onClick={quoteClose}
                  ></Button>
                </Flex>
              )}
              <Flex className={classNames({ "knowledge-base": allFile.length > 0 })}>
                {allFile && (isRightShow || isLeftShow) && (
                  <Flex>
                    {isRightShow && (
                      <Flex className="right-icon">
                        <Button
                          shape="circle"
                          icon={<RightOutlined />}
                          className="right"
                          onClick={() => translateX(1)}
                        />
                      </Flex>
                    )}
                    {isLeftShow && (
                      <Flex className="left-icon">
                        <Button shape="circle" icon={<LeftOutlined />} className="left" onClick={() => translateX(2)} />
                      </Flex>
                    )}
                  </Flex>
                )}
                <Flex className="knowledge-base-info">
                  {allFile &&
                    allFile.map((item, index) => {
                      return (
                        <Flex
                          key={index}
                          className={classNames("knowledge-content-base", {
                            "knowledge-content-base-width": allFile.length > 1 && !item.loading,
                          })}
                        >
                          {item.loading && (
                            <Flex className="knowledge-load" align="center">
                              <Spin spinning={item.loading}></Spin>
                            </Flex>
                          )}
                          <Flex
                            className="knowledge-content-base-flex"
                            align="center"
                            style={{ width: !item.loading ? "100%" : "calc(100% - 28px)" }}
                          >
                            <Flex className="knowledge-content-base-item" align="center">
                              {item.flag === "knowledge" && (
                                <Flex className="sino-relation-icon">{knowdgeSVGIcon.mainImage1}</Flex>
                              )}
                              {(item.flag === "file" || item.flag === "know") && fileExtensionHandler(item)}
                              {(item.flag === "webPageFile" || item.flag === "oneWebPageFile") && (
                                <img width="24" height="24" src={item.faviocn} alt="" />
                              )}
                              <Flex className="knowledge-base-item first-title" vertical justify="center">
                                <div className="knowledge-base-title">{item.libName}</div>
                                <div className="two-title">{item.libDesc}</div>
                              </Flex>
                            </Flex>
                            {!item.loading && (
                              <CloseCircleFilled className="close" onClick={() => handleDeleteKnowledge(item, index)} />
                            )}
                          </Flex>
                        </Flex>
                      );
                    })}
                </Flex>
              </Flex>
              {tags && tags.length > 0 && (
                <Flex className="prompt-select">
                  {tags.map((tag) => (
                    <Tag
                      key={tag.id}
                      bordered={false}
                      closable
                      onClose={() => {
                        tagClose();
                      }}
                    >
                      {tag.title}
                    </Tag>
                  ))}
                </Flex>
              )}
              <Flex className="chat-textarea">
                {/* {isDown && (
                <Flex className="chat-cue" vertical>
                  <Flex className="chat-cue-tit">选择提示词</Flex>
                  <Flex vertical className="chat-cue-con">
                    {promptList?.map((item: Prompt, index: number) => {
                      return (
                        <Flex className="chat-cue-text">
                          <span className="prompt-item-txt">{item.title}</span>
                        </Flex>
                      );
                    })}
                  </Flex>
                </Flex>
              )} */}
                <Flex>
                  <Flex
                    className="chat-cue"
                    ref={popupContainerRef}
                    // style={{
                    //   display: promptList.length > 0 ? "Flex" : "none",
                    // }}
                    children={""}
                  ></Flex>
                </Flex>
                <Mentions
                  prefix={["/", "#"]}
                  // loading={loading}
                  autoFocus={true}
                  // placement="top"
                  // getPopupContainer={(triggerNode: { parentNode: any }) =>
                  //   triggerNode.parentNode as any.parentNode.parentNode.parentNode
                  // }
                  className="text-input-mentions"
                  // notFoundContent={<span className="placeholder">输入关键字来搜索提示词</span>}
                  // notFoundContent={<span></span>}
                  placeholder="请输入内容，输入【/】选择指令或【#】选择提示词"
                  rows={4}
                  value={query}
                  // disabled={sseChat.progress == GenerationProgress.RESPONDING}
                  maxLength={10000}
                  onKeyDown={handleKeyDown}
                  options={[]}
                  onInput={(e) => {
                    let value = (e.target as HTMLInputElement).value;

                    // 检查内容是否只包含空格或回车符
                    if (/^[\s]*$/.test(value)) {
                      setQuery(""); // 如果只包含空格或回车符，清空输入框
                    } else {
                      setQuery(value); // 否则更新输入内容
                    }
                  }}
                  onSearch={(text, trigger) => {
                    // setLoading(true);
                    const lastPrefix = lastPrefixRef.current;
                    const isSamePrefix =
                      (lastPrefix === "/" && trigger === "/") || (lastPrefix === "#" && trigger === "#");

                    if (dropdownVisible && isSamePrefix) {
                      setDropdownVisible(false);
                    } else {
                      setLoading(true);
                      debouncePromptSearch(text, currentAi, aiList, trigger, agentId);
                      lastPrefixRef.current = trigger; // 更新上一次的 prefix
                    }
                    // 设置当前触发的 prefix
                    setTriggerPrefix(trigger);
                  }}
                  // onSelect={(options) => {
                  //   setPrompt(promptList[options.key]);
                  //   setQuery("");
                  //   setPromptList([]);
                  // }}
                  // options={promptList?.map((item: Prompt, index: number) => {
                  //   return {
                  //     style: {
                  //       display: "flex",
                  //       gap: "8px",
                  //     },
                  //     value: `${item.title}`,
                  //     key: `${index}`,
                  //     label: (
                  //       <>
                  //         <img src={browser.runtime.getURL("/images/prompt/prompt-card-title.svg")} alt="" />
                  //         <span className="prompt-item-txt">{item.title}</span>
                  //       </>
                  //     ),
                  //   };
                  // })}
                />
              </Flex>
            </Flex>
            <Flex className="operate">
              <ConfigProvider getPopupContainer={() => popupContainerRef.current}>
                <Dropdown
                  menu={{
                    items: promptList.length
                      ? promptList.map((item) => {
                          const menuItem: any = {
                            key: item.key,
                            label: item.label,
                          };
                          if (Array.isArray(item.children) && item.children.length > 0) {
                            menuItem.children = item.children;
                          }
                          return menuItem;
                        })
                      : [
                          {
                            key: "no-data",
                            label: (
                              <Empty
                                image={Empty.PRESENTED_IMAGE_SIMPLE}
                                description="暂无数据"
                                style={{ margin: 0, padding: "8px 12px" }}
                              />
                            ),
                            disabled: true,
                          },
                        ],
                    onClick: handlePromptClick,
                  }}
                  trigger={["click"]}
                  onOpenChange={(visible) => {
                    setDropdownVisible(visible);
                  }}
                  getPopupContainer={() => popupContainerRef.current}
                  open={dropdownVisible}
                  disabled={sseChat.progress == GenerationProgress.RESPONDING}
                >
                  <Button
                    className={classNames(
                      "cue-word-btn",
                      sseChat.progress != GenerationProgress.RESPONDING && "cue-word-btn-hover",
                    )}
                    onClick={handleButtonClick}
                    icon={<IconFont type="PromptOutlined" className="icon" />}
                  ></Button>
                </Dropdown>
              </ConfigProvider>
              <Button
                disabled={sseChat.progress == GenerationProgress.RESPONDING || !query}
                className={classNames("send-btn", query.trim() && "active")}
                onClick={() => handleSubmit(false)}
              >
                {sseChat.progress === GenerationProgress.RESPONDING || !query
                  ? createChatIcon.unselected
                  : createChatIcon.selected}
              </Button>
            </Flex>
          </Flex>

          <HistoryDrawer
            agentId={agentId}
            userInfo={userInfo}
            visible={openHistoryDrawer}
            handleClose={historyDrawerClose}
            handleBack={historyCallback}
          />
          {/* {knowledModel && (
          <Knowledge
            cardData={cardData}
            knowledModel={knowledModel}
            knowledLoading={knowledLoading}
            keywordSearch={keywordSearch}
            keywordValue={searchParams?.entity?.libName}
            closeKnowledModel={closeKnowledModel}
            closeKnowledModelFalg={closeKnowledModelFalg}
            onCheckboxChange={handleCheckboxChange}
          ></Knowledge>
        )} */}
          {webPageFileModal && (
            <WebPageFilechild
              webCardData={webPageFile}
              webPageFileModal={webPageFileModal}
              closeWebPageModel={closeWebPageModel}
              closeWebPageFalg={closeWebPageFalg}
              onCheckboxWebPageChange={handleCheckboxWebPageChange}
            ></WebPageFilechild>
          )}
          <Drawer
            title="知识库"
            placement="bottom"
            onClose={onCloseDrawer}
            className="knowledge-drawer"
            contentWrapperStyle={{ height: "664px", boxShadow: "none" }}
            getContainer={() => {
              if (document.getElementById("shadow-side-panel")) {
                const shadowDom = document.getElementById("shadow-side-panel").shadowRoot;
                return shadowDom.querySelector(".side-panel-content");
              }
            }}
            open={openDrawer}
          >
            <SelectKnowledgeData
              openDrawer={openDrawer}
              onGetKnowledge={onGetKnowledge}
              selectKnowledgeArr={selectKnowledgeArr}
              selectKnIdsArr={selectKnIdsArr}
              onCloseFun={closeFun}
            />
          </Drawer>
        </Flex>
      )}
    </>
  );
};

export default Chat;
