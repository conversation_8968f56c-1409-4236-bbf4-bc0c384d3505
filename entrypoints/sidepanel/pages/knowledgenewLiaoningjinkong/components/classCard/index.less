.class-card {
  background: var(--ant-color-fill-tertiary);
  padding: var(--ant-padding-sm) var(--ant-padding-xxs) var(--ant-padding-sm) var(--ant-padding-sm);
  border: 1px solid var(--ant-color-border-secondary);
  border-radius: var(--ant-border-radius);
  flex: 1;
  cursor: pointer;
  .btn-icon-card{
    width: 20px !important;
    height:20px !important;
    border: 1px solid #fff;
    box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.12)
  }
  .knowledge-name{
    font-weight: bold;
    font-size: var(--ant-font-size);
    line-height: 24px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    word-break: break-all;
    -webkit-box-orient: vertical;
  }
  .knowledge-checkbox{
    opacity: 0
  }
  &:hover{
    border: 1px solid var(--ant-color-primary-border-hover);
    .knowledge-checkbox{
      opacity: 1
    }
  }
}