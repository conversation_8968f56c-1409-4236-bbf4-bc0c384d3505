import React from "react";
import { But<PERSON>, Checkbox, <PERSON>lex, <PERSON>con<PERSON>rm, Tag, Tooltip, theme } from "antd";
import { DeleteOutlined, SelectOutlined } from "@ant-design/icons";
import classnames from "classnames";
import type { MenuProps } from "antd";
import "./index.less";
import IconFont from "@/components/IconFont";

type Props = {
  data: {
    id: string;
    name: string;
  };
  checked?: boolean;
  onCheckChange?: (checked: boolean) => void;
};
const { useToken } = theme;
const KnowledgeCard: React.FC<Props> = ({ data, checked = false, onCheckChange }) => {
  const { token } = useToken();
  return (
    <Flex className="class-card" vertical gap={token.marginXXS}>
      <Flex justify="space-between">
        <Flex align="center" gap={token.marginXXS}>
          <Button
            style={data.id == "0-0" ? { backgroundColor: "#f5f5f5" } : { backgroundColor: token.geekblue1 }}
            className="btn-icon-card"
            type="text"
            size="small"
            icon={
              <IconFont
                className="icon"
                type="knowledgeBaseOutlined"
                fill={data.id == "0-0" ? "#000" : token.geekblue6}
              />
            }
          />
        </Flex>
        {/* <Checkbox
          value={data.id}
          className="knowledge-checkbox"
          checked={checked}
          onChange={(e) => onCheckChange?.(e.target.checked)}
        /> */}
      </Flex>

      <Flex style={{ fontWeight: "bold", fontSize: token.fontSize }} vertical>
        <Flex className="knowledge-name">{data.name}</Flex>
      </Flex>
    </Flex>
  );
};

export default KnowledgeCard;
