import { Col, Flex, Row, Spin, theme, Button, message, Tooltip } from "antd"; // 引入 Spin 组件
import React, { useEffect, useState } from "react";

import IconFont from "@/components/IconFont";
import AnswerTips from "./answerTips";
import TopTitle from "../../components/TopTitle";
import { getToken, getTenantId } from "@/utils/auth.ts";
import "./index.less";
import EmptyData from "@/components/EmptyData";
import { RightOutlined } from "@ant-design/icons";
const LAB_FEATURES_ENABLED: string = import.meta.env["VITE_ENABLE_LAB_FEATURES"];
import { getContainer } from "@/utils";
// 工作间
import shuzhifenxi from "./images/workshop/shuzhifenxi.png";
import jinkongwenhua from "./images/workshop/jinkongwenhua.png";
// 学习室
import jiwei from "./images/studyRoom/jiwei.png";
import duxing from "./images/studyRoom/duxing.png";
import xingwu from "./images/studyRoom/xingwu.png";
import denggao from "./images/studyRoom/denggao.png";
// 工具箱
import hetongshenchazhushou from "./images/toolboxWeb/hetongshenchazhushou.png";
import zhidujiansuozhushou from "./images/toolboxWeb/zhidujiansuozhushou.png";
import yueduzhushou from "./images/toolboxWeb/yueduzhushou.png";
import wendangfenxizhushou from "./images/toolboxWeb/wendangfenxizhushou.png";
import zhongyinghuyi from "./images/toolboxWeb/zhongyinghuyi.png";
import wendangduibi from "./images/toolboxWeb/wendangduibi.png";

const { useToken } = theme;
const ToolLiaoningjinkong: React.FC = ({ toKnowledge }) => {
  const fetchRequest = useFetchRequest();
  const [selectedId, setSelectedId] = useState("");
  const [sceneList, setSceneList] = useState([]);
  const [loading, setLoading] = useState(false); // 新增状态变量
  const { token: csstoken } = useToken();

  const [isComponent, setIsComponent] = useState("");

  const handleBack = () => {
    setSelectedId("");
  };

  const getData = () => {
    let arr = [
      {
        catId: "1",
        catName: "工作间",
        scenes: [
          {
            sceneCatName: "数智分析",
            sceneDesc: "数智分析，洞见未来，赋能决策新维度～",
            sceneUrl: `${import.meta.env["VITE_TOOLBOX_URL"]}/#/contract-query-assistant`,
            sceneIconUrl: shuzhifenxi,
            agentid: "967e49fe-8f53-4bb1-8ac0-ac13e701facf",
            toToolbox: true,
            isOpen: true,
          },
          {
            sceneCatName: "金控文华",
            sceneDesc: "金控文华，妙笔生花，祝您写作更胜一筹～",
            sceneUrl: `${import.meta.env["VITE_TOOLBOX_URL"]}/#/document-assistant`,
            sceneIconUrl: jinkongwenhua,
            agentid: "",
            toToolbox: true,
            isOpen: true,
          },
        ],
      },
      {
        catId: "2",
        catName: "学习室",
        scenes: [
          {
            sceneCatName: "积微 · ",
            sceneDesc:
              "积土成山，风雨兴焉；积水成渊，蛟龙生焉！一点积累，一点总结，将零散的知识串联为完整的知识体系，开启你的“不知道学习学不完”的阶梯旅程。",
            sceneUrl: "",
            sceneIconUrl: jiwei,
            agentid: "",
            sceneCatNameDesc: "知识积累器",
            toToolbox: false,
            isOpen: true,
            component: "knowledge",
          },
          {
            sceneCatName: "笃行 · ",
            sceneDesc:
              "纸上得来终觉浅，绝知此事要躬行！将所学内容逐步全面理论理解，可以在这里真正的做到学以致用。“学而不用等于不学”的知识。",
            sceneUrl: "",
            sceneIconUrl: duxing,
            agentid: "",
            sceneCatNameDesc: "实战授权柜",
            toToolbox: false,
            isOpen: false,
          },
          {
            sceneCatName: "省吾 · ",
            sceneDesc:
              "吾日三省吾身，为人谋而不忠乎！重点都错学到哪方面分析，提供一定程度个人认知，进而落实适合的学习策略，便能进行针对性系统化。",
            sceneUrl: "",
            sceneIconUrl: xingwu,
            agentid: "",
            sceneCatNameDesc: "学习诊断镜",
            toToolbox: false,
            isOpen: false,
          },
          {
            sceneCatName: "登高 · ",
            sceneDesc:
              "会当凌绝顶，一览众山小，可以高瞻远瞩！聚焦全局行业动态，洞悉尖端趋势环境化动态，便能大幅度提高行业认知自己，是塑铸行业标杆。",
            sceneUrl: "",
            sceneIconUrl: denggao,
            agentid: "",
            sceneCatNameDesc: "行业瞭望台",
            toToolbox: false,
            isOpen: false,
          },
        ],
      },
      {
        catId: "3",
        catName: "工具箱",
        scenes: [
          {
            sceneCatName: "合同审查助手",
            sceneDesc: "",
            sceneUrl: `${import.meta.env["VITE_TOOLBOX_URL"]}/#/contract-scene-set`,
            sceneIconUrl: hetongshenchazhushou,
            agentId: "30aa3cb4-dc4e-4ab6-91e3-6fd68087005a",
            toToolbox: true,
            isOpen: true,
          },
          {
            sceneCatName: "文档对比",
            sceneDesc: "",
            sceneUrl: `${import.meta.env["VITE_TOOLBOX_URL"]}/#/business-contracts-comparison-second`,
            sceneIconUrl: wendangduibi,
            agentid: "75537247-5ed2-4348-bae6-b631cd9a48bb",
            toToolbox: true,
            isOpen: true,
          },
          {
            sceneCatName: "制度检索系统",
            sceneDesc: "",
            sceneUrl: "",
            sceneIconUrl: zhidujiansuozhushou,
            toToolbox: false,
            isOpen: false,
          },
          {
            sceneCatName: "快速阅读助手",
            sceneDesc: "",
            sceneUrl: "",
            sceneIconUrl: yueduzhushou,
            toToolbox: false,
            isOpen: false,
          },
          {
            sceneCatName: "单文档内容分析助手",
            sceneDesc: "",
            sceneUrl: `${import.meta.env["VITE_TOOLBOX_URL"]}/#/document-analysis-assistant`,
            sceneIconUrl: wendangfenxizhushou,
            agentid: "673cc468-80ad-4904-9005-59714d6cf6b5",
            toToolbox: true,
            isOpen: true,
          },
          {
            sceneCatName: "LNFCSEEK中英互译大师",
            sceneDesc: "",
            sceneUrl: "",
            sceneIconUrl: zhongyinghuyi,
            toToolbox: false,
            isOpen: false,
          },
        ],
      },
    ];
    setSceneList(arr);
    // setLoading(true); // 开始加载数据时设置 loading 为 true
    // fetchRequest({
    //   api: "getSceneList",
    //   params: {},
    //   callback(res) {
    //     setLoading(false); // 数据加载完成后设置 loading 为 false
    //     if (res.code === 200) {
    //       setSceneList(res.data);
    //     }
    //   },
    // });
  };

  const updateUrlParams = (url, params) => {
    // Split into base URL and hash (if any)
    const [baseUrl, hashWithParams] = url.split("#");

    // Process the base URL (remove all query params)
    const urlObj = new URL(baseUrl);
    urlObj.search = ""; // Clear all query params from the base URL

    let result = urlObj.toString();

    // If there's a hash, handle its query params
    if (hashWithParams) {
      const [hashPath, hashQuery] = hashWithParams.split("?");
      const hashParams = new URLSearchParams(hashQuery || "");

      // Update hash params (add/replace all given params)
      Object.keys(params).forEach((key) => {
        hashParams.delete(key);
        hashParams.set(key, params[key]);
      });

      // Reconstruct the hash with updated params
      let newHash = hashPath;
      if (hashParams.toString()) {
        newHash += `?${hashParams.toString()}`;
      }

      // Append the hash to the result
      result = `${result.split("#")[0]}#${newHash}`;
    }

    return result;
  };

  // const openUrl = async (item: any) => {
  //   let updatedUrl = item?.sceneUrl;
  //   const token = await getToken();
  //   const tenantId = await getTenantId();
  //   updatedUrl = updateUrlParams(item?.sceneUrl, {
  //     tenantid: tenantId,
  //     token,
  //     agentId: item?.agentId || "",
  //     tenantId: tenantId,
  //   });
  //   window.open(updatedUrl, "_blank");
  // };

  useEffect(() => {
    getData();
  }, []); // 空依赖数组，确保只在组件挂载时调用一次

  const toToolbox = async (val: any) => {
    if (val.toToolbox) {
      let updatedUrl = val.sceneUrl;
      const token = await getToken();
      const tenantId = await getTenantId();
      updatedUrl = updateUrlParams(val.sceneUrl, {
        tenantId,
        tenantid: tenantId,
        agentId: val.agentid,
        agentid: val.agentid,
        token,
      });
      window.open(updatedUrl, "_blank");
    } else {
      if (val.isOpen) {
        console.log("------1");
        if (val.component == "knowledge") {
          toKnowledge();
        }
      } else {
        console.log("------12");
        message.open({
          type: "info",
          content: "暂未开通，敬请期待",
        });
      }
    }
  };

  return (
    <>
      {!selectedId ? (
        <Flex vertical className="tool-page-container">
          <TopTitle title="场景"></TopTitle>
          <>
            <Flex className="tool-group-con">
              {sceneList && sceneList.length > 0 ? (
                <Flex
                  vertical
                  style={{
                    flex: "1 1 100%",
                    width: "100%",
                  }}
                >
                  {sceneList.map((item: any) => {
                    return (
                      <Flex className="tool-group-sign" vertical key={item.catId}>
                        <Flex className="tool-group-title">{item.catName}</Flex>
                        <Row gutter={16}>
                          {item.scenes.map((val: any) => {
                            return (
                              <Col className="sino-gutter-row" span={12} key={val.sceneId}>
                                {/* <Flex className="tool-item" onClick={() => openUrl(val)}>
                                  {val.sceneIconUrl ? (
                                    <img
                                      src={
                                        val.sceneIconUrl.includes("//")
                                          ? val.sceneIconUrl
                                          : `${import.meta.env["VITE_OFFICIAL_URL"]}${val.sceneIconUrl}`
                                      }
                                      alt=""
                                      className="icon-img"
                                    />
                                  ) : (
                                    <IconFont type="FileSearchOutline" className="icon" />
                                  )}
                                  <Flex className="sino-gutter-text" vertical>
                                    <Typography.Text>{val.sceneName}</Typography.Text>
                                    <Typography.Text type="secondary">{val.sceneDesc}</Typography.Text>
                                  </Flex>
                                  {!!val.pointsCost && val.pointsCost > 0 && (
                                    <Flex className="sino-gutter-point">
                                      <span style={{ color: csstoken.blue }}>{val.pointsCost}&nbsp;</span>
                                      <IconFont type="PointsCost" className="icon" />
                                    </Flex>
                                  )}
                                </Flex> */}
                                <Flex className="study-room-item" vertical onClick={() => toToolbox(val)}>
                                  <img src={val.sceneIconUrl} alt="" />
                                  <Flex vertical className="study-room-content">
                                    <Flex className="study-room-title" align="center">
                                      {val.sceneCatName}
                                      {val.sceneCatNameDesc && (
                                        <Flex className="study-room-title-desc">{val.sceneCatNameDesc}</Flex>
                                      )}
                                    </Flex>
                                    <Tooltip
                                      getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                                      placement="top"
                                      title={val.sceneDesc}
                                    >
                                      <span className="study-room-desc">{val.sceneDesc}</span>
                                    </Tooltip>
                                    <Button
                                      type="link"
                                      className="study-room-try"
                                      icon={<RightOutlined />}
                                      iconPosition="end"
                                    >
                                      Have a try
                                    </Button>
                                  </Flex>
                                </Flex>
                              </Col>
                            );
                          })}
                        </Row>
                      </Flex>
                    );
                  })}
                </Flex>
              ) : (
                <EmptyData description="暂无数据"></EmptyData>
              )}
            </Flex>
          </>
        </Flex>
      ) : (
        <>{selectedId === "1" && <AnswerTips handleBack={handleBack} />}</>
      )}
    </>
  );
};

export default ToolLiaoningjinkong;
