let guest = null; // guest服务实例，初始化guest后赋值，可直接调用

// 便签最小宽高
export const NOTE_MIN_WIDTH = 345;
export const NOTE_MIN_HEIGHT = 240;
// 便签默认颜色
export const NOTE_THEME = "#fff";
// 模式
export const NOTE_MODE = "absolute";
// 最大高度
export const NOTE_MAX_HEIGHT = 376;
// 便签换肤悬浮框高度
export const NOTE_THEME_PICKER_HEIGHT = 100;
// 便签评论、协同框宽度
export const NOTE_COOPERATION_WIDTH = 214;
// 便签类型
export const PLAIN = "PLAIN";
export const QUOTE = "QUOTE";
export const AI = "AI";
export const QUIZ = "QUIZ";
export const ONQUIZ = "ONQUIZ";

// 便签详情tab
export const NOTE_DETAIL_RELATION = "relation";
export const NOTE_DETAIL_COMMENT = "comment";
export const NOTE_DETAIL_COOPERATION = "cooperation";

// 便签 class 名称
export const NOTE_CLASS_NAME = "sino-note-item-pos";

// 获取地址栏的参数
export const getQueryString = (name) => {
  const reg = new RegExp(`[?&]${name}=([^&#]*)`);
  const match = window.location.search.match(reg);
  return match ? decodeURIComponent(match[1]) : null; // 使用 decodeURIComponent 进行解码
};

// 删除 url 指定 参数
export const getUrlNoQuery = (name: string = "noteId") => {
  const newUrl = new URL(window.location.href);
  newUrl.searchParams.delete(name);
  return newUrl.toString();
};

// 获取当前网页便签
export const getLocationNotes = async () => {
  let url = getUrlNoQuery();
  const res = await getNote(url);
  let note: CopilotNote[] = res ? JSON.parse(res) : [];
  return note;
};

// 添加便签
export const setNote = (value) => {
  let key = getUrlNoQuery("noteId");
  let obj = {};
  obj[key] = JSON.stringify(value);
  browser.storage.local.set(obj);
};

// 获取当前用户的便签
export async function getNote(name): Promise<string> {
  const res = await browser.storage.local.get([`${name}`]);
  return res[`${name}`];
}

// 初始化便签
export const initNotelist = (_guest, noteList) => {
  _guest._initNotelist(noteList);
  guest = _guest;
  noteList.forEach((item) => {
    let tag = {
      target: [],
      coordinates: {},
      baseId: "",
    };
    try {
      tag = JSON.parse(item.tag) || {};
    } catch (error) {
      console.error("获取或解析 noteList 时发生错误:", error);
    }
    item.coordinates = tag.coordinates;
    // 在网页中创建划词根标签
    createBlankLabel(item.coordinates?.left, item.coordinates?.top, tag?.baseId);

    _guest.anchor({
      $tag: item.id,
      $id: item.id,
      $cluster: "user-highlights",
      $highlight: true,
      uri: item.url,
      target: tag.target || [],
      noteStyle: item.noteStyle,
      item: item,
    });
  });
};

export const createBlankLabel = (left: number, top: number, baseId) => {
  const newElement = document.createElement(`div-${baseId}`); // 创建空白标签
  newElement.innerHTML = `空白元素-${baseId}`; // 添加内容并隐藏，使其可被选中并且不显示
  newElement.style.position = "absolute"; // 添加定位信息
  newElement.style.opacity = "0"; // 不展示
  newElement.style.pointerEvents = "none"; // 不允许影响再次划词
  newElement.style.left = `${left || 0}px`; // 添加坐标信息
  newElement.style.top = `${top || 0}px`; // 添加坐标信息
  document.body.append(newElement); // 将新元素插入
};

export const getDay = (timestamp) => {
  const date = new Date(timestamp);
  const year = date.getFullYear();
  const month = date.getMonth() + 1; // 月份是从 0 开始计数的，需要加 1
  const day = date.getDate();
  const formattedDate = year + "-" + month + "-" + day;
  return formattedDate;
};

// 便签位于图钉右侧的临界值
export const pinCriticalLeft = (item) => {
  // 图钉位于浏览器最右侧, 10 为 边缘临界值, 214 是 右侧评论或协同
  return document.body.clientWidth - item.noteStyle.noteWidth - 10 - NOTE_COOPERATION_WIDTH;
};

const NOTE_JUMP_URL_KEY: string = "noteJumpUrl";
const NOTE_JUMP_ID_KEY: string = "noteJumpId";

export const saveDetailJumpInfo = (note) => {
  const { id, url } = note || {};
  browser.storage.local
    .set({
      [NOTE_JUMP_URL_KEY]: url,
      [NOTE_JUMP_ID_KEY]: id,
    })
    .then(() => {
      window.open(url, "_blank");
    });
};

// 便签列表点击打开网页，加载完成后定位到便签位置
export const jumpScrollToNotePos = (_guest) => {
  browser?.storage?.local &&
    browser.storage.local
      .get([NOTE_JUMP_URL_KEY, NOTE_JUMP_ID_KEY])
      .then((res) => {
        if (res[NOTE_JUMP_URL_KEY] && res[NOTE_JUMP_ID_KEY]) {
          if (res[NOTE_JUMP_URL_KEY] === getUrlNoQuery()) {
            let checkCount = 0;
            const maxChecks = 10;
            const intervalTime = 1000; // 每隔 1 秒查找一次
            const intervalId = setInterval(() => {
              checkCount++;
              let dom = document.getElementById("shadow-dom-note");
              let noteEl = null;
              if (dom) {
                const shadowDom = dom.shadowRoot;
                noteEl = shadowDom.getElementById(`note${res[NOTE_JUMP_ID_KEY]}`);
              }
              if (noteEl) {
                _guest._scrollToAnnotation(res[NOTE_JUMP_ID_KEY]); // 滚动到tag位置
                clearInterval(intervalId); // 找到元素后停止查找
              } else if (checkCount >= maxChecks) {
                console.error("未找到元素，停止查找。");
                clearInterval(intervalId); // 达到最大查找次数后停止
              }
            }, intervalTime);
          }
        }
      })
      .finally(() => {
        browser.storage.local.remove([NOTE_JUMP_URL_KEY, NOTE_JUMP_ID_KEY]);
      });
};

// 滚动到tag位置
export const scrollToNoteBase = (tag) => {
  if (guest) {
    guest._scrollToAnnotation(tag);
  }
};

// 展开收起tag
export const setNoteShowClose = (tag, isOpen) => {
  if (guest) {
    guest._setNoteShowClose(tag, isOpen);
  }
};

// 展示隐藏便签
export const showNoteItem = async (notes, flag: boolean = true) => {
  notes.forEach(async (note) => {
    let dom = document.getElementById("shadow-dom-note");
    if (dom) {
      const shadowDom = dom.shadowRoot;
      const noteEl = shadowDom.getElementById(`note${note.id}`);
      if (!noteEl) {
        return;
      }
      if (flag) {
        const coordinates = getRightMiddleCoordinates();
        noteEl.style.left = `${coordinates.x + 248}px`;
        noteEl.style.top = `${coordinates.y}px`;
        noteEl.classList.add("show"); // 展示
      } else {
        noteEl.classList.remove("show"); // 隐藏
      }
      noteEl.classList.add("sino-transition"); // 动画
      setTimeout(() => {
        reRenderNote(note); // 定位
      }, 50);
    }
  });
};
// 获取视口右侧中间的坐标
export const getRightMiddleCoordinates = () => {
  const viewportWidth = window.innerWidth; // 获取视口宽度
  const viewportHeight = window.innerHeight; // 获取视口高度
  const scrollY = window.scrollY; // 获取垂直滚动距离

  const x = viewportWidth; // 右边界的 x 坐标
  const y = scrollY + viewportHeight / 2; // 中间的 y 坐标，加上滚动距离

  return { x, y };
};
// 获取屏幕视口中间的坐标，包括滚动距离
export const getViewportCenterCoordinates = () => {
  // 获取视口的宽度和高度
  const viewportWidth = window.innerWidth;
  const viewportHeight = window.innerHeight;

  // 计算视口中心的坐标
  const x = window.scrollX + viewportWidth / 2;
  const y = window.scrollY + viewportHeight / 2;

  return { x, y };
};

// 更新网页便签位置-动画
export const reRenderNote = (item: CopilotNote) => {
  let dom = document.getElementById("shadow-dom-note");
  if (dom) {
    const shadowDom = dom.shadowRoot;
    const notePanel = shadowDom.getElementById(`note${item.id}`);
    notePanel.style.position = item.noteStyle.noteType;
    let tag = {
      target: [],
      coordinates: {
        left: 0,
        top: 0,
      },
    };
    try {
      tag = JSON.parse(item.tag) || {};
    } catch (error) {
      console.error("获取或解析 tag 时发生错误:", error);
    }
    if (item.displayFlag === 1) {
      // 展示
      if (item.noteStyle.noteType === NOTE_MODE) {
        notePanel.style.left = `${tag.coordinates?.left || 0}px`;
        notePanel.style.top = `${tag.coordinates?.top || 0}px`;
      } else {
        notePanel.style.top = "0px";
        notePanel.style.left = "0px";
      }
    } else {
      // 隐藏
      const coordinates = getRightMiddleCoordinates();
      notePanel.style.left = `${coordinates.x + 248}px`;
      notePanel.style.top = `${coordinates.y}px`;
    }
    notePanel.style.transform = `translate(${item.noteStyle.noteLeft}px, ${item.noteStyle.noteTop}px)`;
    notePanel.setAttribute("data-x", item.noteStyle.noteLeft + "");
    notePanel.setAttribute("data-y", item.noteStyle.noteTop + "");

    // 等待动画结束后将元素从 DOM 中移除
    notePanel.addEventListener(
      "transitionend",
      () => {
        notePanel.classList.remove("sino-transition"); // 移出动画效果，防止拖拽该元素触发动画
      },
      { once: true },
    );
  }
};

// 更新指定 ID 元素的父元素的位置
export const updateParentPosition = (elementId, newTop, newLeft) => {
  // 获取指定 ID 的元素
  const element = document.getElementById(elementId);

  if (element) {
    // 获取父元素
    const parent = element.parentElement;
    if (parent) {
      // 更新父元素的位置
      parent.style.position = "absolute"; // 确保父元素可以自由移动
      parent.style.top = `${newTop}px`;
      parent.style.left = `${newLeft}px`;
    } else {
      console.error("未找到父元素");
    }
  } else {
    console.error("未找到指定 ID 的元素");
  }
};

// 鼠标右键-创建便签-触发文本选中-触发监听文本选中事件
export const createNote = (message, noteMouseX, noteMouseY) => {
  console.log("🔍 createNote called with:", { message, noteMouseX, noteMouseY });
  const baseId = new Date().getTime();
  const newElement = document.createElement(`div-${baseId}`); // 创建空白标签
  newElement.innerHTML = `空白元素-${baseId}`; // 添加内容并隐藏，使其可被选中并且不显示
  newElement.style.position = "absolute"; // 添加定位信息
  newElement.style.opacity = "0"; // 添加定位信息
  newElement.style.pointerEvents = "none"; // 不允许影响再次划词
  newElement.style.left = `${message.mouse ? message.mouse.x : noteMouseX}px`; // 添加坐标信息
  newElement.style.top = `${message.mouse ? message.mouse.y : noteMouseY}px`; // 添加坐标信息
  document.body.append(newElement); // 将新元素插入

  // 添加延迟确保元素被正确插入到DOM中
  setTimeout(() => {
    console.log("🔍 creating selection for element:", newElement);
    const range = document.createRange(); //
    range.selectNodeContents(newElement);
    console.log(
      "🔍 range created:",
      range,
      "collapsed:",
      range.collapsed,
      "start:",
      range.startOffset,
      "end:",
      range.endOffset,
    );
    const selection = window.getSelection();
    selection.removeAllRanges(); // 清除当前选择
    selection.addRange(range); // 添加新范围
    console.log("🔍 selection created, current selection:", selection.toString());
    console.log("🔍 selection range count:", selection.rangeCount);
    if (selection.rangeCount > 0) {
      const selectedRange = selection.getRangeAt(0);
      console.log("🔍 selected range:", selectedRange, "collapsed:", selectedRange.collapsed);
    }

    // 手动触发 selectionchange 事件
    console.log("🔍 manually triggering selectionchange event");
    const selectionChangeEvent = new Event("selectionchange", { bubbles: true });
    document.dispatchEvent(selectionChangeEvent);

    console.log(newElement, 23333333333);
  }, 10); // 10ms延迟
};
